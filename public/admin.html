<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ww小岸 - 后台管理面板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', '-apple-system', 'sans-serif'],
                    },
                    colors: {
                        'glass': 'rgba(255, 255, 255, 0.1)',
                        'glass-border': 'rgba(255, 255, 255, 0.2)',
                        'admin-primary': '#1e293b',
                        'admin-secondary': '#334155',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'slide-down': 'slideDown 0.6s ease-out',
                        'scale-in': 'scaleIn 0.4s ease-out',
                        'float': 'float 6s ease-in-out infinite',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            min-height: 100vh;
        }

        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
            z-index: -1;
            animation: float 8s ease-in-out infinite;
        }

        /* 毛玻璃效果 */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.25);
        }

        .glass-dark {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .modal-content-admin {
            animation: modalOpenAdmin 0.4s ease-out;
        }
        @keyframes modalOpenAdmin {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        .admin-loader {
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spinAdmin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spinAdmin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* 新增样式 */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .slide-down {
            animation: slideDown 0.6s ease-out;
        }

        .slide-up {
            animation: slideUp 0.6s ease-out;
        }

        .scale-in {
            animation: scaleIn 0.4s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* 表格行悬停效果 */
        .table-row {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .table-row:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        /* 状态徽章 */
        .status-badge {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .status-badge:hover {
            transform: scale(1.05);
        }

        /* 按钮悬停效果 */
        .btn-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
        }

        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 修复select元素在毛玻璃背景上的可见性 */
        select.glass {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        select.glass option {
            background: rgba(30, 41, 59, 0.95);
            color: white;
            padding: 8px;
        }

        select.glass option:hover,
        select.glass option:focus {
            background: rgba(59, 130, 246, 0.8);
            color: white;
        }

        /* 修复datetime-local输入框的样式 */
        input[type="datetime-local"].glass {
            color: white;
            color-scheme: dark;
        }

        input[type="datetime-local"].glass::-webkit-calendar-picker-indicator {
            filter: invert(1);
            opacity: 0.7;
        }

        /* 修复checkbox样式 */
        input[type="checkbox"] {
            accent-color: #3b82f6;
        }
        @media (max-width: 768px) {
            .responsive-table thead {
                display: none;
            }
            .responsive-table tbody,
            .responsive-table tr,
            .responsive-table td {
                display: block;
            }
            .responsive-table tr {
                border: 1px solid #ddd;
                margin-bottom: 0.625rem;
                border-radius: 0.375rem;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                padding: 0.5rem;
            }
            .responsive-table td {
                padding-left: 50%;
                position: relative;
                text-align: right;
                border-bottom: 1px solid #eee;
            }
            .responsive-table td:last-child {
                border-bottom: 0;
            }
            .responsive-table td::before {
                content: attr(data-label);
                position: absolute;
                left: 0.75rem;
                width: 45%;
                padding-right: 0.625rem;
                white-space: nowrap;
                text-align: left;
                font-weight: 600;
                color: #4b5563;
            }
            .responsive-table td[data-label="群头像"] img {
                margin-left: auto;
                margin-right: 0;
            }
            .responsive-table td[data-label="操作"] .action-buttons {
                justify-content: flex-end;
            }
        }
        /* iOS风格的样式增强 */
        .bg-white {
            background-color: rgba(255, 255, 255, 0.95);
        }
        
        .rounded-2xl {
            border-radius: 1rem;
        }
        
        .rounded-xl {
            border-radius: 0.75rem;
        }
        
        .shadow-sm {
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05), 0 1px 5px 0 rgba(0, 0, 0, 0.03);
        }
        
        /* 移除冲突的模态框样式，使用glass-strong类 */
        
        /* 平滑过渡效果 */
        details summary::-webkit-details-marker {
            display: none;
        }
        
        details[open] summary svg {
            transform: rotate(180deg);
        }
        
        details > div {
            animation: slideDown 0.3s ease-out;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        /* 按钮样式增强 */
        button {
            transition: all 0.2s ease;
        }
        
        button:active {
            transform: scale(0.97);
        }
        
        /* 毛玻璃效果 */
        #bindingModal {
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }
        
        /* 表格间距优化 */
        #bindingsTable td {
            padding: 0.75rem;
        }
        
        /* 添加动画过渡 */
        .order-card, .bg-white {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .order-card:hover, .bg-white:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        /* 分页导航样式 */
        .pagination-button {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 2.5rem;
            min-width: 2.5rem;
            padding: 0 0.75rem;
            margin: 0 0.25rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.75rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .pagination-button:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
        }

        .pagination-button.active {
            background: rgba(59, 130, 246, 0.8);
            color: white;
            border-color: rgba(59, 130, 246, 0.5);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .pagination-button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .pagination-ellipsis {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 2.5rem;
            padding: 0 0.5rem;
            margin: 0 0.25rem;
            color: rgba(255, 255, 255, 0.5);
        }
        /* 搜索输入框样式 */
        .search-input {
            @apply transition-all duration-300 border-gray-300 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50;
        }
        .search-input:focus {
            @apply shadow-md;
        }
        /* 空状态样式优化 */
        .empty-state {
            @apply py-16 flex flex-col items-center justify-center text-center;
        }
        
        /* 激活状态标识样式 */
        .active-status-badge {
            @apply inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium;
        }
        .active-status-badge.active {
            @apply bg-blue-100 text-blue-800;
        }
        .active-status-badge.inactive {
            @apply bg-gray-100 text-gray-600;
        }
        
        /* 订单卡片激活状态样式 */
        .order-item.active {
            @apply border-l-4 border-blue-500;
        }
    </style>
</head>
<body class="min-h-screen p-4 sm:p-6 md:p-8 fade-in">
    <div class="container mx-auto max-w-7xl">
        <!-- 头部标题 -->
        <header class="mb-8 text-center slide-down">
            <div class="inline-flex items-center justify-center w-16 h-16 glass rounded-2xl mb-4">
                <i data-lucide="shield-check" class="w-8 h-8 text-white"></i>
            </div>
            <h1 class="text-4xl font-bold text-white mb-2">ww小岸</h1>
            <p class="text-white/80 text-lg font-medium">后台管理面板</p>
        </header>

        <!-- 登录表单 -->
        <section id="password-section" class="glass-strong p-8 rounded-3xl shadow-2xl max-w-lg mx-auto scale-in">
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-12 h-12 glass rounded-xl mb-4">
                    <i data-lucide="lock" class="w-6 h-6 text-white"></i>
                </div>
                <h2 class="text-2xl font-semibold text-white mb-2">管理员登录</h2>
                <p class="text-white/70">请输入管理员密码以继续</p>
            </div>

            <form id="password-form" class="space-y-6">
                <div class="space-y-2">
                    <label for="admin-password" class="block text-sm font-medium text-white/90">管理员密码</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <i data-lucide="key" class="w-5 h-5 text-white/50"></i>
                        </div>
                        <input type="password" id="admin-password" name="admin-password" required
                            autocomplete="current-password"
                            placeholder="请输入管理员密码"
                            class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 border-0 focus:ring-2 focus:ring-white/30 transition-all">
                    </div>
                </div>

                <button type="submit" id="loginButton"
                    class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-4 px-6 rounded-2xl btn-hover flex items-center justify-center space-x-2">
                    <span class="button-text">进入管理后台</span>
                    <span class="admin-loader hidden"></span>
                    <i data-lucide="arrow-right" class="w-5 h-5 button-icon"></i>
                </button>
                <div id="password-error" class="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl text-red-200 hidden" aria-live="polite"></div>
            </form>
        </section>

        <!-- 管理面板主要内容 -->
        <section id="bindings-list-section" class="hidden fade-in">
            <!-- 顶部导航栏 -->
            <div class="glass-strong rounded-3xl p-6 mb-8 slide-down">
                <div class="flex flex-col lg:flex-row justify-between items-center gap-6">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 glass rounded-xl flex items-center justify-center">
                            <i data-lucide="database" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-semibold text-white">绑定管理</h2>
                            <p class="text-white/70">管理所有订单绑定信息</p>
                        </div>
                    </div>

                    <div class="flex flex-wrap gap-3">
                        <!-- 排序控制 -->
                        <div class="glass rounded-2xl p-1 flex items-center">
                            <span class="text-sm text-white/80 ml-3 mr-2">排序:</span>
                            <button id="sortOrderButton"
                                class="flex items-center text-sm px-4 py-2 glass rounded-xl text-white btn-hover">
                                <i data-lucide="arrow-down" id="sortIcon" class="w-4 h-4 mr-2"></i>
                                <span id="sortOrderText">降序</span>
                            </button>
                        </div>

                        <!-- 添加绑定按钮 -->
                        <button id="addBindingButton"
                            class="bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold py-3 px-6 rounded-2xl btn-hover flex items-center space-x-2">
                            <i data-lucide="plus" class="w-5 h-5"></i>
                            <span>添加新绑定</span>
                        </button>
                    </div>
                </div>
            </div>
            <!-- 搜索和筛选区域 -->
            <div class="glass rounded-3xl p-6 mb-8 slide-up">
                <div class="flex flex-col lg:flex-row gap-4">
                    <!-- 搜索框 -->
                    <div class="flex-1 relative">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-5 h-5 text-white/50"></i>
                        </div>
                        <input id="searchInput" type="text" placeholder="搜索群号、订单号或用户名..."
                            class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 border-0 focus:ring-2 focus:ring-white/30 transition-all">
                    </div>

                    <!-- 筛选按钮 -->
                    <div class="flex gap-2">
                        <button class="glass rounded-2xl px-4 py-2 text-white/80 hover:text-white btn-hover flex items-center space-x-2">
                            <i data-lucide="filter" class="w-4 h-4"></i>
                            <span>筛选</span>
                        </button>
                        <button class="glass rounded-2xl px-4 py-2 text-white/80 hover:text-white btn-hover flex items-center space-x-2">
                            <i data-lucide="download" class="w-4 h-4"></i>
                            <span>导出</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div id="loadingMessage" class="text-center py-16 text-white/60 hidden">
                <div class="admin-loader inline-block mr-3"></div>
                <span>加载中，请稍候...</span>
            </div>

            <!-- 空状态 -->
            <div id="noRecordsMessage" class="text-center py-16 text-white/60 hidden">
                <i data-lucide="inbox" class="w-16 h-16 mx-auto mb-4 opacity-50"></i>
                <h3 class="text-lg font-semibold text-white mb-2">暂无数据</h3>
                <p class="text-white/60">还没有任何绑定记录</p>
            </div>

            <!-- 数据展示区域 -->
            <div class="space-y-4">
                <!-- 桌面端表格视图 -->
                <div class="hidden lg:block">
                    <div class="glass rounded-3xl overflow-hidden">
                        <table id="bindingsTable" class="w-full hidden">
                            <thead class="glass-dark">
                                <tr class="text-white/90">
                                    <th class="px-6 py-4 text-left text-sm font-semibold">序号</th>
                                    <th class="px-6 py-4 text-left text-sm font-semibold">群信息</th>
                                    <th class="px-6 py-4 text-left text-sm font-semibold">订单详情</th>
                                    <th class="px-6 py-4 text-left text-sm font-semibold">档位</th>
                                    <th class="px-6 py-4 text-left text-sm font-semibold">状态</th>
                                    <th class="px-6 py-4 text-left text-sm font-semibold">到期时间</th>
                                    <th class="px-6 py-4 text-left text-sm font-semibold">操作</th>
                                </tr>
                            </thead>
                            <tbody id="bindings-table-body" class="divide-y divide-white/10">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 移动端卡片视图 -->
                <div class="lg:hidden" id="bindings-cards-container">
                    <!-- 卡片将通过JavaScript动态生成 -->
                </div>
            </div>
            <!-- 分页控制部分 -->
            <div id="paginationControls" class="mt-8 glass rounded-3xl p-6 hidden">
                <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                    <!-- 分页信息 -->
                    <div id="paginationInfo" class="text-white/80 text-sm">
                        显示 <span id="currentRange" class="font-semibold text-white">1-5</span> 条，共 <span id="totalItems" class="font-semibold text-white">0</span> 条
                    </div>

                    <!-- 分页按钮 -->
                    <div class="flex items-center space-x-2">
                        <button id="prevPageBtn"
                            class="glass rounded-xl px-4 py-2 text-white/80 hover:text-white btn-hover flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i data-lucide="chevron-left" class="w-4 h-4"></i>
                            <span>上一页</span>
                        </button>

                        <div id="pageNumbers" class="flex space-x-1"></div>

                        <button id="nextPageBtn"
                            class="glass rounded-xl px-4 py-2 text-white/80 hover:text-white btn-hover flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span>下一页</span>
                            <i data-lucide="chevron-right" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <!-- 绑定模态框 -->
    <div id="bindingModal"
        class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 hidden"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="modal-content-admin glass-strong rounded-3xl shadow-2xl overflow-hidden max-w-lg w-full transform transition-all">
            <!-- 模态框头部 -->
            <div class="p-6 border-b border-white/20">
                <div class="flex justify-between items-start">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 glass rounded-xl flex items-center justify-center">
                            <i data-lucide="edit" class="w-5 h-5 text-white"></i>
                        </div>
                        <h3 id="bindingModalTitle" class="text-xl font-semibold text-white">编辑绑定</h3>
                    </div>
                    <button type="button" id="closeBindingModal"
                        class="glass rounded-xl p-2 text-white/60 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6">
                <form id="bindingForm" class="space-y-6">
                    <input type="hidden" id="originalOrderNumber">
                    <input type="hidden" id="originalSkuId">

                    <!-- 订单号 -->
                    <div class="space-y-2">
                        <label for="modalOrderNumber" class="block text-sm font-medium text-white/90">订单号</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="receipt" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="text" id="modalOrderNumber" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 border-0 focus:ring-2 focus:ring-white/30 transition-all">
                        </div>
                    </div>

                    <!-- 群号 -->
                    <div class="space-y-2">
                        <label for="modalGroupNumber" class="block text-sm font-medium text-white/90">绑定群号</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="users" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="text" id="modalGroupNumber" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 border-0 focus:ring-2 focus:ring-white/30 transition-all">
                        </div>
                    </div>

                    <!-- 档位选择 -->
                    <div class="space-y-2">
                        <label for="modalSkuId" class="block text-sm font-medium text-white/90">选择档位</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="tag" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <select id="modalSkuId" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white border-0 focus:ring-2 focus:ring-white/30 transition-all appearance-none">
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                <i data-lucide="chevron-down" class="w-5 h-5 text-white/50"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 所有者 -->
                    <div id="modalOwnerContainer" class="space-y-2">
                        <label for="modalOwner" class="block text-sm font-medium text-white/90">所有者</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="user" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="text" id="modalOwner" placeholder="默认为admin"
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 border-0 focus:ring-2 focus:ring-white/30 transition-all">
                        </div>
                        <p class="text-xs text-white/60">留空则默认为admin</p>
                    </div>

                    <!-- 时间设置 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 剩余天数 -->
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <label for="modalRemainingDays" class="block text-sm font-medium text-white/90">剩余天数</label>
                                <div class="flex items-center space-x-2">
                                    <input type="checkbox" id="modalPermanentExpiration"
                                        class="w-4 h-4 text-blue-600 bg-transparent border-white/30 rounded focus:ring-blue-500 focus:ring-2">
                                    <label for="modalPermanentExpiration" class="text-sm text-white/80">永久有效</label>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <i data-lucide="calendar-days" class="w-5 h-5 text-white/50"></i>
                                </div>
                                <input type="number" id="modalRemainingDays" min="1" max="3650"
                                    class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 border-0 focus:ring-2 focus:ring-white/30 transition-all">
                            </div>
                            <p class="text-xs text-white/60">输入订单剩余天数，最长10年</p>
                        </div>

                        <!-- 到期时间 -->
                        <div class="space-y-2">
                            <label for="modalExpirationTimestamp" class="block text-sm font-medium text-white/90">到期时间</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <i data-lucide="clock" class="w-5 h-5 text-white/50"></i>
                                </div>
                                <input type="datetime-local" id="modalExpirationTimestamp"
                                    class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white border-0 focus:ring-2 focus:ring-white/30 transition-all">
                            </div>
                            <p class="text-xs text-white/60">选择具体的到期时间</p>
                        </div>
                    </div>

                    <!-- 激活状态 -->
                    <div class="space-y-2">
                        <label for="modalIsActive" class="block text-sm font-medium text-white/90">激活状态</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="power" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <select id="modalIsActive"
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white border-0 focus:ring-2 focus:ring-white/30 transition-all appearance-none">
                                <option value="0">待激活</option>
                                <option value="1">激活中</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                <i data-lucide="chevron-down" class="w-5 h-5 text-white/50"></i>
                            </div>
                        </div>
                        <p class="text-xs text-white/60">每个群组只能有一个激活状态的订单</p>
                    </div>

                    <!-- 错误提示 -->
                    <div id="bindingModalError" class="p-4 bg-red-500/20 border border-red-500/30 rounded-2xl text-red-200 hidden" aria-live="polite"></div>
                </form>
            </div>

            <!-- 模态框底部 -->
            <div class="p-6 border-t border-white/20">
                <div class="flex flex-col sm:flex-row gap-3">
                    <button type="button" id="cancelBindingButton"
                        class="flex-1 glass rounded-2xl py-3 px-6 text-white/80 hover:text-white font-medium btn-hover flex items-center justify-center space-x-2">
                        <i data-lucide="x" class="w-4 h-4"></i>
                        <span>取消</span>
                    </button>
                    <button type="submit" id="saveBindingButton" form="bindingForm"
                        class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-3 px-6 rounded-2xl btn-hover flex items-center justify-center space-x-2">
                        <span class="button-text">保存</span>
                        <span class="admin-loader hidden"></span>
                        <i data-lucide="check" class="w-4 h-4 button-icon"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- 通知组件 -->
    <div id="toast-notification"
        class="fixed bottom-6 right-6 glass-strong rounded-2xl shadow-2xl hidden transition-all duration-300 ease-in-out z-[100] max-w-sm">
        <div class="p-4 flex items-center space-x-3">
            <div class="w-8 h-8 glass rounded-xl flex items-center justify-center">
                <i data-lucide="info" class="w-4 h-4 text-white"></i>
            </div>
            <p id="toast-message" class="text-white font-medium flex-1"></p>
            <button onclick="hideToast()" class="glass rounded-lg p-1 text-white/60 hover:text-white transition-colors">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>
    </div>

    <script>
        // 隐藏通知函数
        function hideToast() {
            const toast = document.getElementById('toast-notification');
            if (toast) {
                toast.classList.add('hidden');
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // 初始化Lucide图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            // --- 1. DOM 元素获取 (全部移入DOMContentLoaded内部) ---
            // 这样做可以保证在获取元素时，它们一定已经存在于页面上。
            const passwordSection = document.getElementById('password-section');
            const passwordForm = document.getElementById('password-form');
            const adminPasswordInput = document.getElementById('admin-password');
            const loginButton = document.getElementById('loginButton');
            const passwordErrorDiv = document.getElementById('password-error');
            const bindingsListSection = document.getElementById('bindings-list-section');
            const bindingsTable = document.getElementById('bindingsTable');
            const bindingsTableBody = document.getElementById('bindings-table-body');
            const loadingMessageDiv = document.getElementById('loadingMessage');
            const noRecordsMessageDiv = document.getElementById('noRecordsMessage');
            const addBindingButtonElem = document.getElementById('addBindingButton');
            const bindingModal = document.getElementById('bindingModal');
            const bindingModalTitle = document.getElementById('bindingModalTitle');
            const bindingForm = document.getElementById('bindingForm');
            const originalOrderNumberInput = document.getElementById('originalOrderNumber');
            const originalSkuIdInput = document.getElementById('originalSkuId');
            const modalOrderNumberInput = document.getElementById('modalOrderNumber');
            const modalGroupNumberInput = document.getElementById('modalGroupNumber');
            const modalSkuIdSelect = document.getElementById('modalSkuId');
            const modalOwnerInput = document.getElementById('modalOwner');
            const modalExpirationTimestampInput = document.getElementById('modalExpirationTimestamp');
            const modalPermanentExpirationCheckbox = document.getElementById('modalPermanentExpiration');
            const saveBindingButton = document.getElementById('saveBindingButton');
            const cancelBindingButton = document.getElementById('cancelBindingButton');
            const closeBindingModalButton = document.getElementById('closeBindingModal');
            const bindingModalErrorDiv = document.getElementById('bindingModalError');
            const toastNotification = document.getElementById('toast-notification');
            const toastMessage = document.getElementById('toast-message');
            const sortOrderButton = document.getElementById('sortOrderButton');
            const sortOrderText = document.getElementById('sortOrderText');
            const sortIcon = document.getElementById('sortIcon');
            const searchInput = document.getElementById('searchInput');
            const paginationControls = document.getElementById('paginationControls');
            const paginationInfo = document.getElementById('paginationInfo');
            const prevPageBtn = document.getElementById('prevPageBtn');
            const nextPageBtn = document.getElementById('nextPageBtn');
            const pageNumbers = document.getElementById('pageNumbers');
            const currentRangeSpan = document.getElementById('currentRange');
            const totalItemsSpan = document.getElementById('totalItems');
    
            // --- 2. 配置与状态变量 ---
            const SKUID_15_VALUE = '6b9d46742cc311f094855254001e7c00';
            const SKUID_20_VALUE = '590a339e2cbb11f0983452540025c377';
            const SKU_CONFIG = {
                [SKUID_15_VALUE]: '15元档位',
                [SKUID_20_VALUE]: '20元档位',
            };
            let currentAdminPassword = '';
            let isDescending = true;
            let currentPage = 1;
            let itemsPerPage = 5;
            let allGroups = [];
            let filteredGroups = [];
    
            // --- 3. 函数定义 ---
            function toggleButtonLoading(button, isLoading, defaultText = "保存") {
                const textSpan = button.querySelector('.button-text');
                const loaderSpan = button.querySelector('.admin-loader');
                const iconSpan = button.querySelector('.button-icon');

                if (isLoading) {
                    button.disabled = true;
                    button.classList.add('opacity-75', 'cursor-not-allowed');
                    if (textSpan) textSpan.classList.add('hidden');
                    if (loaderSpan) loaderSpan.classList.remove('hidden');
                    if (iconSpan) iconSpan.classList.add('hidden');
                } else {
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');
                    if (textSpan) {
                        textSpan.classList.remove('hidden');
                        textSpan.textContent = defaultText;
                    }
                    if (loaderSpan) loaderSpan.classList.add('hidden');
                    if (iconSpan) iconSpan.classList.remove('hidden');

                    // 重新初始化图标
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                }
            }
    
            function showToast(message, duration = 3000) {
                toastMessage.textContent = message;
                toastNotification.classList.remove('hidden');
                toastNotification.style.transform = 'translateX(100%)';
                toastNotification.style.opacity = '0';

                // 动画进入
                setTimeout(() => {
                    toastNotification.style.transform = 'translateX(0)';
                    toastNotification.style.opacity = '1';
                }, 10);

                // 自动隐藏
                setTimeout(() => {
                    toastNotification.style.transform = 'translateX(100%)';
                    toastNotification.style.opacity = '0';
                    setTimeout(() => {
                        toastNotification.classList.add('hidden');
                    }, 300);
                }, duration);

                // 重新初始化图标
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }
    
            function populateSkuSelect() {
                modalSkuIdSelect.innerHTML = '<option value="" disabled selected>请选择档位</option>';
                for (const id in SKU_CONFIG) {
                    const option = document.createElement('option');
                    option.value = id;
                    option.textContent = SKU_CONFIG[id];
                    modalSkuIdSelect.appendChild(option);
                }
            }
    
            function displayError(element, message) {
                element.textContent = message;
                element.classList.toggle('hidden', !message);
            }
    
            function showLoadingState(isLoading) {
                loadingMessageDiv.classList.toggle('hidden', !isLoading);
                bindingsTable.classList.toggle('hidden', isLoading);
                noRecordsMessageDiv.classList.add('hidden');
                paginationControls.classList.add('hidden');
                paginationInfo.classList.add('hidden');
            }
    
            function escapeHtml(unsafe) {
                if (typeof unsafe !== 'string') return unsafe;
                return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
            }
    
            function formatDateForDateTimeLocal(date) {
                if (!(date instanceof Date) || isNaN(date)) {
                    const now = new Date();
                    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
                    return now.toISOString().slice(0, 16);
                }
                const tempDate = new Date(date.getTime());
                const year = tempDate.getFullYear();
                const month = (tempDate.getMonth() + 1).toString().padStart(2, '0');
                const day = tempDate.getDate().toString().padStart(2, '0');
                const hours = tempDate.getHours().toString().padStart(2, '0');
                const minutes = tempDate.getMinutes().toString().padStart(2, '0');
                return `${year}-${month}-${day}T${hours}:${minutes}`;
            }
    
            async function confirmUpgrade(orderId, orderNumber) {
                const confirmation = confirm(`您确定要将订单 "${escapeHtml(orderNumber)}" (${SKU_CONFIG[SKUID_15_VALUE]}) 升级吗？\n\n该订单的剩余价值将被等价转换为 ${SKU_CONFIG[SKUID_20_VALUE]} 的时长，原订单将被删除。此操作不可撤销。`);
                if (!confirmation) return;
                showToast('正在处理升级...', 3000);
                try {
                    const response = await fetch('/admin/bindings/upgrade', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json', 'X-Admin-Password': currentAdminPassword },
                        body: JSON.stringify({ id: parseInt(orderId, 10) })
                    });
                    const result = await response.json();
                    if (response.ok && result.success) {
                        showToast(result.message || '升级成功！', 4000);
                        await fetchAndPopulateBindings();
                    } else {
                        throw new Error(result.message || `升级失败 (状态: ${response.status})`);
                    }
                } catch (error) {
                    showToast(`升级失败: ${error.message}`, 5000);
                }
            }
    
            function processBindingsData(data) {
                allGroups = [];
                filteredGroups = [];
                currentPage = 1;
                if (!data || data.length === 0) {
                    noRecordsMessageDiv.classList.remove('hidden');
                    bindingsTable.classList.add('hidden');
                    paginationControls.classList.add('hidden');
                    paginationInfo.classList.add('hidden');
                    return;
                }
                allGroups = data;
                sortAndFilterGroups();
                renderCurrentPage();
            }
    
            function renderGroupRow(group, index) {
                const row = bindingsTableBody.insertRow();
                row.className = 'table-row text-white hover:bg-white/10';
                const groupAvatarUrl = group.groupNumber ? `https://p.qlogo.cn/gh/${escapeHtml(group.groupNumber)}/${escapeHtml(group.groupNumber)}/100` : 'https://placehold.co/40x40/cccccc/333333?text=N/A';
                let groupRemainingDaysText = '';
                let expirationClass = '';
                if (group.hasPermanent) {
                    groupRemainingDaysText = '永久';
                    expirationClass = 'bg-green-100 text-green-800';
                } else if (group.totalRemainingDays > 0) {
                    groupRemainingDaysText = `${group.totalRemainingDays}天`;
                    expirationClass = 'bg-green-100 text-green-800';
                } else {
                    groupRemainingDaysText = '已过期';
                    expirationClass = 'bg-red-100 text-red-800';
                }
                const skuTypesList = Array.from(group.skuTypes).join(' / ');
                const groupExpirationText = group.hasPermanent ? '永久有效' : (group.totalRemainingDays > 0 ? `约剩 ${group.totalRemainingDays} 天` : '已过期');
                let ordersDetailHtml = '';
                group.items.forEach(item => {
                    let itemRemainingDaysText = '';
                    let itemExpirationClass = '';
                    let canBeUpgraded = false;
                    if (item.remainingDays === -1) {
                        itemRemainingDaysText = '永久';
                        itemExpirationClass = 'bg-green-100 text-green-800';
                    } else if (item.remainingDays > 0) {
                        itemRemainingDaysText = `${item.remainingDays}天`;
                        itemExpirationClass = 'bg-green-100 text-green-800';
                        if (item.skuId === SKUID_15_VALUE) canBeUpgraded = true;
                    } else {
                        itemRemainingDaysText = '已用完';
                        itemExpirationClass = 'bg-red-100 text-red-800';
                    }
                    let upgradeButtonHtml = '';
                    if (canBeUpgraded) {
                        upgradeButtonHtml = `
                            <button class="upgrade-order-button p-1.5 glass rounded-xl text-green-200 hover:text-white hover:bg-white/20 transition-colors btn-hover"
                                    data-id="${item.id}" data-order-number="${escapeHtml(item.orderNumber)}" title="升级到20元档位">
                                <i data-lucide="arrow-up" class="w-4 h-4"></i>
                            </button>`;
                    }
                    ordersDetailHtml += `
                    <div class="flex items-center justify-between p-3 border-b border-white/10 last:border-0">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-white">${escapeHtml(item.orderNumber)}</span>
                                <span class="ml-2 px-2 py-0.5 text-xs rounded-xl glass text-white">${itemRemainingDaysText}</span>
                                ${item.isActive ? '<span class="ml-2 px-2 py-0.5 text-xs rounded-xl bg-yellow-500/20 text-yellow-200 border border-yellow-500/30">激活中</span>' : ''}
                            </div>
                            <div class="mt-1 flex items-center text-xs text-white/70">
                                <span class="mr-2">${escapeHtml(item.skuType)}</span><span class="mr-2">•</span><span>所有者: ${escapeHtml(item.owner || 'admin')}</span>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            ${upgradeButtonHtml}
                            <button class="edit-order-button p-1.5 glass rounded-xl text-blue-200 hover:text-white hover:bg-white/20 transition-colors btn-hover"
                                    data-order-number="${escapeHtml(item.orderNumber)}" data-sku-id="${escapeHtml(item.skuId)}" title="编辑">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="delete-order-button p-1.5 glass rounded-xl text-red-200 hover:text-white hover:bg-white/20 transition-colors btn-hover"
                                    data-order-number="${escapeHtml(item.orderNumber)}" data-sku-id="${escapeHtml(item.skuId)}" data-sku-type="${escapeHtml(item.skuType)}" title="删除">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>`;
                });
                row.innerHTML = `
                <td colspan="8" class="p-4">
                    <div class="glass rounded-2xl shadow-lg border border-white/20 overflow-hidden card-hover">
                        <div class="p-4 sm:p-5 flex items-start">
                            <div class="flex-shrink-0 mr-4">
                                <img src="${groupAvatarUrl}" alt="群头像" class="h-14 w-14 rounded-xl object-cover shadow-lg border-2 border-white/20" onerror="this.onerror=null;this.src='https://placehold.co/56x56/667eea/ffffff?text=群';">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-semibold text-white truncate">群号: ${escapeHtml(group.groupNumber)}</h3>
                                    <span class="inline-flex items-center px-3 py-1 glass rounded-xl text-sm font-medium text-white">${groupRemainingDaysText}</span>
                                </div>
                                <div class="mt-1 flex flex-wrap items-center text-sm text-white/80">
                                    <span class="mr-3">群组状态: ${groupExpirationText}</span><span class="mr-3">•</span><span>档位: ${skuTypesList}</span>
                                </div>
                                <div class="mt-1 text-sm text-white/60">订单数量: ${group.items.length}</div>
                                <div class="mt-3 flex">
                                    <button class="add-to-group-button text-sm px-3 py-1.5 glass rounded-xl text-white hover:bg-white/20 transition-colors flex items-center btn-hover" data-group-number="${escapeHtml(group.groupNumber)}">
                                        <i data-lucide="plus" class="w-4 h-4 mr-1"></i>
                                        添加订单到此群
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="border-t border-white/20 glass-dark rounded-b-2xl">
                            <details class="group">
                                <summary class="flex items-center justify-between p-4 cursor-pointer hover:bg-white/10 transition-colors">
                                    <span class="text-sm font-medium text-white">查看订单详情 (${group.items.length})</span>
                                    <i data-lucide="chevron-down" class="w-5 h-5 text-white/60 group-open:rotate-180 transition-transform"></i>
                                </summary>
                                <div class="px-4 pb-4 glass-dark rounded-b-2xl divide-y divide-white/10">${ordersDetailHtml}</div>
                            </details>
                        </div>
                    </div>
                </td>`;
                row.querySelectorAll('.upgrade-order-button').forEach(button => button.addEventListener('click', () => confirmUpgrade(button.dataset.id, button.dataset.orderNumber)));
                row.querySelector('.add-to-group-button')?.addEventListener('click', () => showBindingModal('添加订单到群组', { groupNumber: row.querySelector('.add-to-group-button').dataset.groupNumber }));
                row.querySelectorAll('.edit-order-button').forEach(button => button.addEventListener('click', () => {
                    const orderItem = group.items.find(item => item.orderNumber === button.dataset.orderNumber && item.skuId === button.dataset.skuId);
                    if (orderItem) showBindingModal('编辑绑定', orderItem);
                }));
                row.querySelectorAll('.delete-order-button').forEach(button => button.addEventListener('click', () => confirmDeleteBinding(button.dataset.orderNumber, button.dataset.skuId, button.dataset.skuType)));

                // 重新初始化图标
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }
    
            function sortAndFilterGroups() {
                const searchTerm = searchInput.value.trim().toLowerCase();
                filteredGroups = searchTerm === '' ? [...allGroups] : allGroups.filter(group => group.groupNumber.toLowerCase().includes(searchTerm) || group.items.some(item => item.orderNumber.toLowerCase().includes(searchTerm)));
                filteredGroups.sort((a, b) => {
                    if (a.hasPermanent && !b.hasPermanent) return isDescending ? -1 : 1;
                    if (!a.hasPermanent && b.hasPermanent) return isDescending ? 1 : -1;
                    return isDescending ? b.totalRemainingDays - a.totalRemainingDays : a.totalRemainingDays - b.totalRemainingDays;
                });
                updatePaginationInfo();
            }
    
            function showBindingModal(title, bindingData = null) {
                bindingModalTitle.textContent = title;
                bindingForm.reset();
                populateSkuSelect();
                displayError(bindingModalErrorDiv, '');
                modalPermanentExpirationCheckbox.checked = false;
                // 关键：在这里可以安全地设置disabled属性，因为变量现在肯定不是null
                modalExpirationTimestampInput.disabled = false;
                modalExpirationTimestampInput.value = '';
                if (bindingData) {
                    if (bindingData.groupNumber && !bindingData.orderNumber) {
                        originalOrderNumberInput.value = '';
                        originalSkuIdInput.value = '';
                        modalGroupNumberInput.value = bindingData.groupNumber;
                        modalOwnerInput.value = '';
                        const defaultExpiration = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
                        modalExpirationTimestampInput.value = formatDateForDateTimeLocal(defaultExpiration);
                        saveBindingButton.querySelector('.button-text').textContent = '添加绑定';
                    } else {
                        originalOrderNumberInput.value = bindingData.orderNumber;
                        originalSkuIdInput.value = bindingData.skuId;
                        modalOrderNumberInput.value = bindingData.orderNumber;
                        modalGroupNumberInput.value = bindingData.groupNumber;
                        modalSkuIdSelect.value = bindingData.skuId;
                        modalOwnerInput.value = bindingData.owner || '';
                        if (bindingData.remainingDays === -1) {
                            modalPermanentExpirationCheckbox.checked = true;
                            modalExpirationTimestampInput.disabled = true;
                        } else {
                            modalPermanentExpirationCheckbox.checked = false;
                            modalExpirationTimestampInput.disabled = false;
                            let dateToSet = bindingData.expirationISOString ? new Date(bindingData.expirationISOString) : null;
                            if (!dateToSet || isNaN(dateToSet.getTime()) || dateToSet.getTime() <= Date.now()) {
                                dateToSet = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
                            }
                            modalExpirationTimestampInput.value = formatDateForDateTimeLocal(dateToSet);
                        }
                        saveBindingButton.querySelector('.button-text').textContent = '更新绑定';
                    }
                } else {
                    originalOrderNumberInput.value = '';
                    originalSkuIdInput.value = '';
                    modalOwnerInput.value = '';
                    const defaultExpiration = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
                    modalExpirationTimestampInput.value = formatDateForDateTimeLocal(defaultExpiration);
                    saveBindingButton.querySelector('.button-text').textContent = '添加绑定';
                }
                bindingModal.classList.remove('hidden');
            }
    
            function hideBindingModal() {
                bindingModal.classList.add('hidden');
            }
    
            function updateSortUI() {
                sortOrderText.textContent = isDescending ? '降序' : '升序';
                sortIcon.setAttribute('data-lucide', isDescending ? 'arrow-down' : 'arrow-up');

                // 重新初始化图标
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }
    
            function createPageButton(pageNum, isActive = false) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `pagination-button ${isActive ? 'active' : ''}`;
                pageBtn.textContent = pageNum;
                pageBtn.addEventListener('click', () => { currentPage = pageNum; renderCurrentPage(); });
                return pageBtn;
            }
    
            function createEllipsis() {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'pagination-ellipsis';
                ellipsis.textContent = '...';
                return ellipsis;
            }
    
            function updatePaginationInfo() {
                const totalGroups = filteredGroups.length;
                const totalPages = Math.max(1, Math.ceil(totalGroups / itemsPerPage));
                if (currentPage > totalPages) currentPage = totalPages;
                prevPageBtn.disabled = currentPage === 1;
                nextPageBtn.disabled = currentPage === totalPages;
                pageNumbers.innerHTML = '';
                let startPage = Math.max(1, currentPage - 2), endPage = Math.min(totalPages, startPage + 4);
                if (endPage - startPage < 4 && totalPages > 4) startPage = Math.max(1, endPage - 4);
                if (startPage > 1) {
                    pageNumbers.appendChild(createPageButton(1));
                    if (startPage > 2) pageNumbers.appendChild(createEllipsis());
                }
                for (let i = startPage; i <= endPage; i++) pageNumbers.appendChild(createPageButton(i, i === currentPage));
                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) pageNumbers.appendChild(createEllipsis());
                    pageNumbers.appendChild(createPageButton(totalPages));
                }
                const start = (currentPage - 1) * itemsPerPage + 1;
                const end = Math.min(start + itemsPerPage - 1, totalGroups);
                currentRangeSpan.textContent = totalGroups === 0 ? '0-0' : `${start}-${end}`;
                totalItemsSpan.textContent = totalGroups;
                paginationControls.classList.toggle('hidden', totalGroups <= itemsPerPage);
                paginationInfo.classList.toggle('hidden', totalGroups === 0);
                noRecordsMessageDiv.classList.toggle('hidden', totalGroups > 0);
                bindingsTable.classList.toggle('hidden', totalGroups === 0);
            }
    
            function renderCurrentPage() {
                bindingsTableBody.innerHTML = '';
                if (filteredGroups.length === 0) return;
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = Math.min(startIndex + itemsPerPage, filteredGroups.length);
                for (let i = startIndex; i < endIndex; i++) renderGroupRow(filteredGroups[i], i);
                updatePaginationInfo();
            }
    
            async function fetchAndPopulateBindings() {
                showLoadingState(true);
                try {
                    const response = await fetch('/admin/bindings', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json', 'X-Admin-Password': currentAdminPassword },
                        body: JSON.stringify({ password: currentAdminPassword })
                    });
                    if (!response.ok) {
                        if (response.status === 401) {
                            displayError(passwordErrorDiv, '密码已失效或不正确，请重新输入。');
                            passwordSection.classList.remove('hidden');
                            bindingsListSection.classList.add('hidden');
                            currentAdminPassword = '';
                            throw new Error('认证失败');
                        }
                        const errorResult = await response.json().catch(() => ({ message: '获取绑定列表失败。' }));
                        throw new Error(errorResult.message || `HTTP error ${response.status}`);
                    }
                    const result = await response.json();
                    if (result.success && Array.isArray(result.data)) {
                        processBindingsData(result.data);
                        passwordSection.classList.add('hidden');
                        bindingsListSection.classList.remove('hidden');
                    } else {
                        throw new Error(result.message || '获取到的数据格式不正确。');
                    }
                } catch (error) {
                    if (error.message !== '认证失败') {
                        noRecordsMessageDiv.innerHTML = `<p>加载失败: ${error.message}</p>`;
                        noRecordsMessageDiv.classList.remove('hidden');
                        bindingsTable.classList.add('hidden');
                    }
                } finally {
                    showLoadingState(false);
                }
            }
    
            function confirmDeleteBinding(orderNumber, skuId, skuType) {
                if (confirm(`您确定要删除订单号 "${escapeHtml(orderNumber)}" (${escapeHtml(skuType)}) 的绑定吗？此操作不可恢复。`)) {
                    deleteBinding(orderNumber, skuId);
                }
            }
    
            async function deleteBinding(orderNumber, skuId) {
                showToast('正在删除...', 2000);
                try {
                    const response = await fetch('/admin/bindings/delete', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json', 'X-Admin-Password': currentAdminPassword },
                        body: JSON.stringify({ orderNumber, skuId })
                    });
                    const result = await response.json();
                    if (response.ok && result.success) {
                        showToast(result.message || '删除成功！');
                        fetchAndPopulateBindings();
                    } else {
                        throw new Error(result.message || `删除失败 (状态: ${response.status})`);
                    }
                } catch (error) {
                    showToast(`删除失败: ${error.message}`, 5000);
                }
            }
    
            async function handleBindingFormSubmit(event) {
                event.preventDefault();
                const isEditMode = !!originalOrderNumberInput.value;
                const defaultButtonText = isEditMode ? "更新绑定" : "添加绑定";
                toggleButtonLoading(saveBindingButton, true, defaultButtonText);
                displayError(bindingModalErrorDiv, '');
                const isPermanent = modalPermanentExpirationCheckbox.checked;
                const expirationTimestampValue = modalExpirationTimestampInput.value;
                const owner = modalOwnerInput.value.trim();
                const orderNumber = modalOrderNumberInput.value.trim();
                const groupNumber = modalGroupNumberInput.value.trim();
                const skuId = modalSkuIdSelect.value;
                if (!orderNumber || !groupNumber || !skuId) {
                    displayError(bindingModalErrorDiv, '订单号、群号和档位均不能为空。');
                    toggleButtonLoading(saveBindingButton, false, defaultButtonText);
                    return;
                }
                if (!isPermanent && !expirationTimestampValue) {
                    displayError(bindingModalErrorDiv, '请选择一个到期时间，或勾选 "永久有效"。');
                    toggleButtonLoading(saveBindingButton, false, defaultButtonText);
                    return;
                }
                let url, body;
                if (isEditMode) {
                    url = '/admin/bindings/update';
                    body = {
                        originalOrderNumber: originalOrderNumberInput.value,
                        originalSkuId: originalSkuIdInput.value,
                        newOrderNumber: orderNumber,
                        newGroupNumber: groupNumber,
                        newSkuId: skuId,
                        isPermanent: isPermanent,
                        expirationTimestamp: !isPermanent ? new Date(expirationTimestampValue).toISOString() : null,
                        owner: owner || 'admin'
                    };
                } else {
                    url = '/admin/bindings/add';
                    body = {
                        orderNumber: orderNumber,
                        groupNumber: groupNumber,
                        skuId: skuId,
                        isPermanent: isPermanent,
                        expirationTimestamp: !isPermanent ? new Date(expirationTimestampValue).toISOString() : null,
                        owner: owner || 'admin'
                    };
                }
                try {
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json', 'X-Admin-Password': currentAdminPassword },
                        body: JSON.stringify(body)
                    });
                    const result = await response.json();
                    if (response.ok && result.success) {
                        hideBindingModal();
                        showToast(result.message || (isEditMode ? '更新成功！' : '添加成功！'));
                        fetchAndPopulateBindings();
                    } else {
                        displayError(bindingModalErrorDiv, result.message || `操作失败 (状态: ${response.status})`);
                    }
                } catch (error) {
                    displayError(bindingModalErrorDiv, '请求失败，请检查网络或联系管理员。');
                } finally {
                    toggleButtonLoading(saveBindingButton, false, defaultButtonText);
                }
            }
    
            // --- 4. 初始化和事件监听器 ---
            populateSkuSelect();
            updateSortUI();
            adminPasswordInput.focus();
    
            passwordForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                const password = adminPasswordInput.value.trim();
                if (!password) {
                    displayError(passwordErrorDiv, '请输入管理员密码。');
                    return;
                }
                toggleButtonLoading(loginButton, true, "进入管理后台");
                displayError(passwordErrorDiv, '');
                currentAdminPassword = password;
                await fetchAndPopulateBindings();
                toggleButtonLoading(loginButton, false, "进入管理后台");
            });
    
            addBindingButtonElem.addEventListener('click', () => showBindingModal('添加新绑定'));
            bindingForm.addEventListener('submit', handleBindingFormSubmit);
            closeBindingModalButton.addEventListener('click', hideBindingModal);
            cancelBindingButton.addEventListener('click', hideBindingModal);
            bindingModal.addEventListener('click', (event) => { if (event.target === bindingModal) hideBindingModal(); });
    
            modalPermanentExpirationCheckbox.addEventListener('change', function () {
                modalExpirationTimestampInput.disabled = this.checked;
                if (this.checked) {
                    modalExpirationTimestampInput.value = '';
                } else if (!modalExpirationTimestampInput.value) {
                    const defaultExpiration = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
                    modalExpirationTimestampInput.value = formatDateForDateTimeLocal(defaultExpiration);
                }
            });
    
            sortOrderButton.addEventListener('click', () => { isDescending = !isDescending; updateSortUI(); sortAndFilterGroups(); renderCurrentPage(); });
            searchInput.addEventListener('input', () => { currentPage = 1; sortAndFilterGroups(); renderCurrentPage(); });
            prevPageBtn.addEventListener('click', () => { if (currentPage > 1) { currentPage--; renderCurrentPage(); } });
            nextPageBtn.addEventListener('click', () => { const totalPages = Math.ceil(filteredGroups.length / itemsPerPage); if (currentPage < totalPages) { currentPage++; renderCurrentPage(); } });
        });
    </script>
</body>
</html>