<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ww小岸 - 自助服务中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', '-apple-system', 'sans-serif'],
                    },
                    colors: {
                        'glass': 'rgba(255, 255, 255, 0.1)',
                        'glass-border': 'rgba(255, 255, 255, 0.2)',
                    },
                    backdropBlur: {
                        'xs': '2px',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'scale-in': 'scaleIn 0.4s ease-out',
                        'float': 'float 6s ease-in-out infinite',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
        }

        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: float 8s ease-in-out infinite;
        }

        /* 毛玻璃效果 */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.25);
        }

        /* 动画关键帧 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(1deg); }
            66% { transform: translateY(5px) rotate(-1deg); }
        }

        /* 表单和视图切换动画 */
        .form-section,
        .initial-view {
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: top;
        }

        .form-section:not(.active),
        .initial-view:not(.active) {
            opacity: 0;
            max-height: 0;
            overflow: hidden;
            transform: translateY(-20px) scale(0.98);
            pointer-events: none;
        }

        .form-section.active,
        .initial-view.active {
            opacity: 1;
            max-height: 2000px;
            transform: translateY(0) scale(1);
        }

        /* 模态框动画 */
        .modal-content {
            animation: modalSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-40px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* 加载动画 */
        .loader {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 卡片悬停效果 */
        .order-card {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .order-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 按钮悬停效果 */
        .btn-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
        }

        /* 输入框聚焦效果 */
        .input-focus {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .input-focus:focus {
            transform: scale(1.02);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        /* 渐变文字 */
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 状态指示器 */
        .status-indicator {
            position: relative;
        }

        .status-indicator::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -12px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .status-active::before {
            background: #10b981;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
            animation: pulse 2s infinite;
        }

        .status-pending::before {
            background: #f59e0b;
            box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
        }

        .status-expired::before {
            background: #ef4444;
            box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>

<body class="min-h-screen p-4 flex items-center justify-center animate-fade-in">
    <!-- 主容器 -->
    <div class="w-full max-w-lg mx-auto">
        <!-- 头部标题 -->
        <div class="text-center mb-8 animate-slide-up">
            <div class="inline-flex items-center justify-center w-16 h-16 glass rounded-2xl mb-4">
                <i data-lucide="zap" class="w-8 h-8 text-white"></i>
            </div>
            <h1 class="text-4xl font-bold text-white mb-2">ww小岸</h1>
            <p class="text-white/80 text-lg font-medium">自助服务中心</p>
        </div>

        <!-- 主卡片 -->
        <div class="glass-strong rounded-3xl p-8 shadow-2xl animate-scale-in">
            <!-- 初始选择界面 -->
            <div class="initial-view active" id="initialView">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-semibold text-white mb-3">欢迎使用</h2>
                    <p class="text-white/70">请选择您要进行的操作</p>
                </div>

                <div class="space-y-4">
                    <button id="showLoginButton"
                        class="w-full glass rounded-2xl py-4 px-6 text-white font-semibold btn-hover group flex items-center justify-center space-x-3">
                        <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                            <i data-lucide="log-in" class="w-5 h-5"></i>
                        </div>
                        <span class="text-lg">登录账户</span>
                    </button>

                    <button id="showRegisterButton"
                        class="w-full glass rounded-2xl py-4 px-6 text-white font-semibold btn-hover group flex items-center justify-center space-x-3">
                        <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                            <i data-lucide="user-plus" class="w-5 h-5"></i>
                        </div>
                        <span class="text-lg">注册账户</span>
                    </button>
                </div>
            </div>

            <!-- 绑定订单界面 -->
            <div class="form-section" id="bindSection">
                <div class="text-center mb-8">
                    <div class="inline-flex items-center justify-center w-12 h-12 glass rounded-xl mb-4">
                        <i data-lucide="link" class="w-6 h-6 text-white"></i>
                    </div>
                    <h2 class="text-2xl font-semibold text-white mb-2">绑定订单</h2>
                    <p class="text-white/70">将您的爱发电订单绑定到QQ群</p>
                </div>

                <form id="bindForm" class="space-y-6">
                    <div class="space-y-2">
                        <label for="bindOrderNumber" class="block text-sm font-medium text-white/90">订单号</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="receipt" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="text" id="bindOrderNumber" name="bindOrderNumber"
                                placeholder="请输入您的爱发电订单号" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 input-focus border-0 focus:ring-2 focus:ring-white/30">
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label for="bindGroupNumber" class="block text-sm font-medium text-white/90">绑定群号</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="users" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="text" id="bindGroupNumber" name="bindGroupNumber"
                                placeholder="请输入您要绑定的QQ群号" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 input-focus border-0 focus:ring-2 focus:ring-white/30">
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-3 pt-4">
                        <button type="submit" id="bindButton"
                            class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-4 px-6 rounded-2xl btn-hover flex items-center justify-center space-x-2">
                            <span class="button-text">确认绑定</span>
                            <span class="loader hidden"></span>
                            <i data-lucide="arrow-right" class="w-5 h-5 button-icon"></i>
                        </button>
                        <button type="button" id="backFromBind"
                            class="px-6 py-4 glass rounded-2xl text-white font-medium btn-hover flex items-center justify-center">
                            <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                            返回
                        </button>
                    </div>
                </form>
            </div>

            <!-- 登录表单界面 -->
            <div class="form-section" id="loginSection">
                <div class="text-center mb-8">
                    <div class="inline-flex items-center justify-center w-12 h-12 glass rounded-xl mb-4">
                        <i data-lucide="log-in" class="w-6 h-6 text-white"></i>
                    </div>
                    <h2 class="text-2xl font-semibold text-white mb-2">用户登录</h2>
                    <p class="text-white/70">登录您的账户以管理订单</p>
                </div>

                <form id="loginForm" class="space-y-6">
                    <div class="space-y-2">
                        <label for="loginUsername" class="block text-sm font-medium text-white/90">用户名</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="user" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="text" id="loginUsername" name="loginUsername"
                                placeholder="请输入您的用户名" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 input-focus border-0 focus:ring-2 focus:ring-white/30">
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label for="loginPassword" class="block text-sm font-medium text-white/90">密码</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="lock" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="password" id="loginPassword" name="loginPassword"
                                placeholder="请输入您的密码" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 input-focus border-0 focus:ring-2 focus:ring-white/30">
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-3 pt-4">
                        <button type="submit" id="loginButton"
                            class="flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold py-4 px-6 rounded-2xl btn-hover flex items-center justify-center space-x-2">
                            <span class="button-text">登录</span>
                            <span class="loader hidden"></span>
                            <i data-lucide="arrow-right" class="w-5 h-5 button-icon"></i>
                        </button>
                        <button type="button" id="backFromLogin"
                            class="px-6 py-4 glass rounded-2xl text-white font-medium btn-hover flex items-center justify-center">
                            <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                            返回
                        </button>
                    </div>

                    <div class="text-center mt-6">
                        <p class="text-white/70">还没有账户？
                            <a href="#" id="switchToRegister" class="text-white font-semibold hover:text-white/80 transition-colors">立即注册</a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- 注册表单界面 -->
            <div class="form-section" id="registerSection">
                <div class="text-center mb-8">
                    <div class="inline-flex items-center justify-center w-12 h-12 glass rounded-xl mb-4">
                        <i data-lucide="user-plus" class="w-6 h-6 text-white"></i>
                    </div>
                    <h2 class="text-2xl font-semibold text-white mb-2">用户注册</h2>
                    <p class="text-white/70">创建您的账户以开始使用</p>
                </div>

                <form id="registerForm" class="space-y-6">
                    <div class="space-y-2">
                        <label for="registerUsername" class="block text-sm font-medium text-white/90">用户名</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="user" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="text" id="registerUsername" name="registerUsername"
                                placeholder="请设置您的用户名" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 input-focus border-0 focus:ring-2 focus:ring-white/30">
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label for="registerEmail" class="block text-sm font-medium text-white/90">电子邮箱</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="mail" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="email" id="registerEmail" name="registerEmail"
                                placeholder="请输入您的电子邮箱" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 input-focus border-0 focus:ring-2 focus:ring-white/30">
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label for="registerPassword" class="block text-sm font-medium text-white/90">密码</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="lock" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="password" id="registerPassword" name="registerPassword"
                                placeholder="请设置您的密码" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 input-focus border-0 focus:ring-2 focus:ring-white/30">
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label for="confirmPassword" class="block text-sm font-medium text-white/90">确认密码</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i data-lucide="shield-check" class="w-5 h-5 text-white/50"></i>
                            </div>
                            <input type="password" id="confirmPassword" name="confirmPassword"
                                placeholder="请再次输入密码" required
                                class="w-full pl-12 pr-4 py-4 glass rounded-2xl text-white placeholder-white/50 input-focus border-0 focus:ring-2 focus:ring-white/30">
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-3 pt-4">
                        <button type="submit" id="registerButton"
                            class="flex-1 bg-gradient-to-r from-indigo-500 to-blue-600 text-white font-semibold py-4 px-6 rounded-2xl btn-hover flex items-center justify-center space-x-2">
                            <span class="button-text">注册</span>
                            <span class="loader hidden"></span>
                            <i data-lucide="arrow-right" class="w-5 h-5 button-icon"></i>
                        </button>
                        <button type="button" id="backFromRegister"
                            class="px-6 py-4 glass rounded-2xl text-white font-medium btn-hover flex items-center justify-center">
                            <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                            返回
                        </button>
                    </div>

                    <div class="text-center mt-6">
                        <p class="text-white/70">已有账户？
                            <a href="#" id="switchToLogin" class="text-white font-semibold hover:text-white/80 transition-colors">立即登录</a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- 用户面板界面 -->
            <div class="form-section" id="userPanel">
                <!-- 用户信息头部 -->
                <div class="flex justify-between items-center mb-8">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 glass rounded-xl flex items-center justify-center">
                            <i data-lucide="user" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-semibold text-white">用户中心</h2>
                            <p id="userWelcome" class="text-white/70">欢迎回来</p>
                        </div>
                    </div>
                    <button id="logoutButton"
                        class="glass rounded-xl px-4 py-2 text-white/80 hover:text-white btn-hover flex items-center space-x-2">
                        <i data-lucide="log-out" class="w-4 h-4"></i>
                        <span>退出</span>
                    </button>
                </div>

                <!-- 账户信息卡片 -->
                <div class="glass rounded-2xl p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white flex items-center">
                            <i data-lucide="info" class="w-5 h-5 mr-2"></i>
                            账户信息
                        </h3>
                    </div>
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="text-center">
                            <p class="text-white/60 text-sm mb-1">用户名</p>
                            <p id="profileUsername" class="text-white font-semibold">-</p>
                        </div>
                        <div class="text-center">
                            <p class="text-white/60 text-sm mb-1">邮箱</p>
                            <p id="profileEmail" class="text-white font-semibold">-</p>
                        </div>
                        <div class="text-center">
                            <p class="text-white/60 text-sm mb-1">注册时间</p>
                            <p id="profileRegisterTime" class="text-white font-semibold">-</p>
                        </div>
                    </div>
                </div>
            
                <!-- 订单列表卡片 -->
                <div class="glass rounded-2xl p-6 mb-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-white flex items-center">
                            <i data-lucide="shopping-bag" class="w-5 h-5 mr-2"></i>
                            我的订单
                        </h3>
                        <div class="flex items-center space-x-3">
                            <button id="refreshOrdersButton"
                                class="glass rounded-xl px-4 py-2 text-white/80 hover:text-white btn-hover flex items-center space-x-2">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                <span>刷新</span>
                            </button>
                            <div id="ordersLoading" class="loader hidden"></div>
                        </div>
                    </div>

                    <div id="userOrdersList" class="space-y-4">
                        <!-- 订单列表将通过JavaScript动态添加 -->
                        <div class="text-center text-white/60 py-8" id="noOrdersMessage">
                            <i data-lucide="package" class="w-12 h-12 mx-auto mb-4 opacity-50"></i>
                            <p>加载中...</p>
                        </div>
                    </div>
                </div>

                <!-- 订单详情模态框 -->
                <div id="orderDetailsModal"
                    class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 hidden transition-all duration-300 ease-in-out"
                    aria-labelledby="modal-title" role="dialog" aria-modal="true">
                    <div class="modal-content glass-strong rounded-3xl shadow-2xl overflow-hidden max-w-lg w-full transform transition-all duration-300 ease-out">
                        <div class="p-6 border-b border-white/20">
                            <div class="flex justify-between items-start">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 glass rounded-xl flex items-center justify-center">
                                        <i data-lucide="file-text" class="w-5 h-5 text-white"></i>
                                    </div>
                                    <h3 id="orderDetailsTitle" class="text-xl font-semibold text-white">订单详情</h3>
                                </div>
                                <button type="button" id="closeOrderDetailsButton"
                                    class="glass rounded-xl p-2 text-white/60 hover:text-white transition-colors">
                                    <i data-lucide="x" class="w-5 h-5"></i>
                                </button>
                            </div>
                        </div>
                        <div class="p-6" id="orderDetailsList">
                            <!-- 订单详情内容 -->
                        </div>
                        <div class="p-6 border-t border-white/20 text-right">
                            <button type="button" id="closeOrderDetailsButton2"
                                class="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-3 px-6 rounded-2xl btn-hover">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
                <!-- 操作按钮 -->
                <div class="space-y-4">
                    <button id="userPanelBindButton"
                        class="w-full bg-gradient-to-r from-green-500 to-blue-600 text-white font-semibold py-4 px-6 rounded-2xl btn-hover flex items-center justify-center space-x-3">
                        <div class="w-8 h-8 bg-white/20 rounded-xl flex items-center justify-center">
                            <i data-lucide="plus" class="w-5 h-5"></i>
                        </div>
                        <span class="text-lg">绑定新订单</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 结果模态框 -->
    <div id="resultModal"
        class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 hidden transition-all duration-300 ease-in-out"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="modal-content glass-strong rounded-3xl shadow-2xl overflow-hidden max-w-lg w-full transform transition-all duration-300 ease-out">
            <div class="p-6 border-b border-white/20">
                <div class="flex justify-between items-start">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 glass rounded-xl flex items-center justify-center">
                            <i data-lucide="info" class="w-5 h-5 text-white"></i>
                        </div>
                        <h3 id="modalTitle" class="text-xl font-semibold text-white">操作结果</h3>
                    </div>
                    <button type="button" id="closeModalButton"
                        class="glass rounded-xl p-2 text-white/60 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="modalBody" class="text-white/90 space-y-3">
                </div>
                <div id="modalError" class="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl text-red-200 hidden">
                </div>
            </div>
            <div class="p-6 border-t border-white/20 text-right">
                <button type="button" id="modalOkButton"
                    class="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-3 px-6 rounded-2xl btn-hover">
                    好的
                </button>
            </div>
        </div>
    </div>

    <script>
    const initialView = document.getElementById('initialView');
    const bindSection = document.getElementById('bindSection');
    const loginSection = document.getElementById('loginSection');
    const registerSection = document.getElementById('registerSection');
    const userPanel = document.getElementById('userPanel');
    
    const showBindButton = document.getElementById('showBindButton');
    const showLoginButton = document.getElementById('showLoginButton');
    const showRegisterButton = document.getElementById('showRegisterButton');
    
    const switchToLoginLink = document.getElementById('switchToLogin');
    const switchToRegisterLink = document.getElementById('switchToRegister');
    
    const userPanelBindButton = document.getElementById('userPanelBindButton');
    const logoutButton = document.getElementById('logoutButton');
    
    const userWelcome = document.getElementById('userWelcome');
    const profileUsername = document.getElementById('profileUsername');
    const profileEmail = document.getElementById('profileEmail');
    const profileRegisterTime = document.getElementById('profileRegisterTime');
    
    const userOrdersList = document.getElementById('userOrdersList');
    const noOrdersMessage = document.getElementById('noOrdersMessage');
    const ordersLoading = document.getElementById('ordersLoading');

    const bindForm = document.getElementById('bindForm');
    const bindButton = document.getElementById('bindButton');
    const bindOrderNumberInput = document.getElementById('bindOrderNumber');
    const bindGroupNumberInput = document.getElementById('bindGroupNumber');
    const backFromBindButton = document.getElementById('backFromBind');
    
    const loginForm = document.getElementById('loginForm');
    const loginButton = document.getElementById('loginButton');
    const backFromLoginButton = document.getElementById('backFromLogin');
    
    const registerForm = document.getElementById('registerForm');
    const registerButton = document.getElementById('registerButton');
    const backFromRegisterButton = document.getElementById('backFromRegister');

    const modal = document.getElementById('resultModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const modalError = document.getElementById('modalError');
    const closeModalButton = document.getElementById('closeModalButton');
    const modalOkButton = document.getElementById('modalOkButton');

    const refreshOrdersButton = document.getElementById('refreshOrdersButton');

    const orderDetailsModal = document.getElementById('orderDetailsModal');
    const orderDetailsTitle = document.getElementById('orderDetailsTitle');
    const orderDetailsList = document.getElementById('orderDetailsList');
    const closeOrderDetailsButton = document.getElementById('closeOrderDetailsButton');
    const closeOrderDetailsButton2 = document.getElementById('closeOrderDetailsButton2');

    // 显示订单详情模态框
    function showOrderDetails(group) {
        orderDetailsTitle.textContent = `群 ${group.groupNumber} 的订单详情`;
        orderDetailsList.innerHTML = '';
        
        // 创建群头像和信息摘要
        const groupInfoHtml = `
            <div class="flex items-center mb-4 pb-4 border-b border-gray-200">
                <img src="https://p.qlogo.cn/gh/${escapeHtml(group.groupNumber)}/${escapeHtml(group.groupNumber)}/100" 
                    alt="群头像" class="h-14 w-14 rounded-lg object-cover shadow-sm border border-gray-200" 
                    onerror="this.onerror=null;this.src='https://placehold.co/56x56/cccccc/333333?text=N/A';">
                <div class="ml-4">
                    <h4 class="font-medium text-gray-900">群号: ${escapeHtml(group.groupNumber)}</h4>
                    <p class="text-sm text-gray-600">共 ${group.items.length} 个订单</p>
                </div>
            </div>
        `;
        orderDetailsList.innerHTML += groupInfoHtml;
        
        // 添加每个订单的详情
        group.items.forEach((item, index) => {
            // 计算单个订单的剩余天数
            let itemRemainingDaysText = '';
            let itemExpirationClass = '';
            
            if (item.expirationDate.includes('永久')) {
                itemRemainingDaysText = '永久';
                itemExpirationClass = 'bg-green-100 text-green-800';
            } else {
                try {
                    const expirationDate = new Date(item.expirationDate);
                    const now = new Date();
                    const diffTime = expirationDate - now;
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    
                    if (diffDays <= 0) {
                        itemRemainingDaysText = '已过期';
                        itemExpirationClass = 'bg-red-100 text-red-800';
                    } else {
                        itemRemainingDaysText = `${diffDays}天`;
                        itemExpirationClass = 'bg-green-100 text-green-800';
                    }
                } catch (e) {
                    itemRemainingDaysText = '无法计算';
                    itemExpirationClass = 'bg-gray-100 text-gray-800';
                }
            }
            
            // 创建订单条目
            const orderItemHtml = `
                <div class="bg-white p-3 rounded-lg border border-gray-200 mb-3 ${index === group.items.length - 1 ? '' : 'mb-3'}">
                    <div class="flex justify-between items-start">
                        <div>
                            <h5 class="font-medium text-gray-900">订单号: ${escapeHtml(item.orderNumber)}</h5>
                        </div>
                        <span class="px-2 py-0.5 rounded-full text-xs font-medium ${itemExpirationClass}">${itemRemainingDaysText}</span>
                    </div>
                    <div class="mt-2 grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">档位:</span> 
                            <span class="font-medium">${escapeHtml(item.skuType)}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">绑定时间:</span>
                            <span class="font-medium">${item.bindTime ? escapeHtml(new Date(item.bindTime).toLocaleDateString()) : '-'}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">到期时间:</span>
                            <span class="font-medium">${escapeHtml(item.expirationDate)}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">所有者:</span>
                            <span class="font-medium">${escapeHtml(item.owner || '您')}</span>
                        </div>
                    </div>
                </div>
            `;
            orderDetailsList.innerHTML += orderItemHtml;
        });
        
        // 显示模态框
        orderDetailsModal.classList.remove('hidden');
    }
    
    // 隐藏订单详情模态框
    function hideOrderDetails() {
        orderDetailsModal.classList.add('hidden');
    }
    
    // 关闭订单详情模态框的事件监听
    closeOrderDetailsButton.addEventListener('click', hideOrderDetails);
    closeOrderDetailsButton2.addEventListener('click', hideOrderDetails);
    orderDetailsModal.addEventListener('click', (event) => {
        if (event.target === orderDetailsModal) {
            hideOrderDetails();
        }
    });
    
    // 检查用户登录状态
    function checkLoginStatus() {
        // 这里应该从本地存储或cookie中获取用户信息
        const user = JSON.parse(localStorage.getItem('user'));
        if (user && user.token) {
            // 用户已登录
            userWelcome.textContent = `欢迎，${user.username}`;
            profileUsername.textContent = user.username;
            profileEmail.textContent = user.email;
            profileRegisterTime.textContent = user.registerTime || '未知';
            return true;
        }
        return false;
    }

    // 刷新订单列表
    refreshOrdersButton.addEventListener('click', () => {
        renderUserOrders();
    });
    
    // 获取用户订单列表
    async function fetchUserOrders() {
        try {
            ordersLoading.style.display = 'inline-block';
            noOrdersMessage.textContent = '加载中...';
            
            const user = JSON.parse(localStorage.getItem('user'));
            if (!user || !user.token) {
                throw new Error('未登录或登录已过期');
            }
            
            const response = await fetch('/user/bindings', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${user.token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.message || '获取订单列表失败');
            }
            
            if (!result.success) {
                throw new Error(result.message || '获取订单列表失败');
            }
            
            return result.data || [];
            
        } catch (error) {
            console.error('获取用户订单列表失败:', error);
            throw error;
        } finally {
            ordersLoading.style.display = 'none';
        }
    }
    
    // 渲染用户订单列表
    async function renderUserOrders() {
        try {
            const orders = await fetchUserOrders();
            
            // 清空现有内容
            userOrdersList.innerHTML = '';
            
            if (!orders.length) {
                noOrdersMessage.innerHTML = `
                    <i data-lucide="package" class="w-12 h-12 mx-auto mb-4 opacity-50"></i>
                    <p>您还没有绑定任何订单</p>
                    <p class="text-sm mt-2 opacity-70">点击下方按钮开始绑定您的第一个订单</p>
                `;
                userOrdersList.appendChild(noOrdersMessage);
                // 重新初始化图标
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
                return;
            }
            
            // 隐藏"无订单"消息
            noOrdersMessage.style.display = 'none';
            
            // 按群号分组订单
            const groupedOrders = {};
            orders.forEach(order => {
                if (!groupedOrders[order.groupNumber]) {
                    groupedOrders[order.groupNumber] = {
                        groupNumber: order.groupNumber,
                        items: [],
                        skuTypes: new Set(),
                        totalDays: 0,
                        finalExpirationDate: null,
                        hasPermanent: false
                    };
                }
                
                // 添加订单到群组
                groupedOrders[order.groupNumber].items.push(order);
                
                // 添加档位
                groupedOrders[order.groupNumber].skuTypes.add(order.skuType);
                
                // 检查是否有永久订单
                if (order.expirationDate.includes('永久')) {
                    groupedOrders[order.groupNumber].hasPermanent = true;
                    return; // 如果有永久订单，不需要继续计算天数
                }
                
                // 如果不是永久订单，计算剩余天数并累加
                try {
                    const expirationDate = new Date(order.expirationDate);
                    const now = new Date();
                    
                    // 只计算未过期订单
                    if (expirationDate > now) {
                        const diffTime = expirationDate - now;
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        
                        // 累加天数
                        groupedOrders[order.groupNumber].totalDays += diffDays;
                        
                        // 更新最终到期日期
                        const finalDate = new Date(now.getTime() + (groupedOrders[order.groupNumber].totalDays * 24 * 60 * 60 * 1000));
                        groupedOrders[order.groupNumber].finalExpirationDate = finalDate;
                    }
                } catch (e) {
                    console.error("计算日期错误:", e);
                }
            });
            
            // 为每个群创建一个卡片
            Object.values(groupedOrders).forEach(group => {
                // 计算群的总有效期显示文本
                let groupExpirationText = '';
                let groupRemainingDaysText = '';
                let expirationClass = '';
                
                if (group.hasPermanent) {
                    groupExpirationText = '永久有效';
                    groupRemainingDaysText = '永久';
                    expirationClass = 'bg-green-100 text-green-800';
                } else if (group.totalDays > 0) {
                    // 格式化最终到期日期
                    const finalDate = group.finalExpirationDate;
                    const year = finalDate.getFullYear();
                    const month = (finalDate.getMonth() + 1).toString().padStart(2, '0');
                    const day = finalDate.getDate().toString().padStart(2, '0');
                    
                    groupExpirationText = `${year}/${month}/${day}`;
                    groupRemainingDaysText = `${group.totalDays}天`;
                    expirationClass = 'bg-green-100 text-green-800';
                } else {
                    groupExpirationText = '已过期';
                    groupRemainingDaysText = '已过期';
                    expirationClass = 'bg-red-100 text-red-800';
                }
                
                // 获取该群的所有档位
                const skuTypesList = Array.from(group.skuTypes).join(' / ');
                
                // 获取群头像
                const groupAvatarUrl = `https://p.qlogo.cn/gh/${escapeHtml(group.groupNumber)}/${escapeHtml(group.groupNumber)}/100`;
                
                // 创建群订单卡片
                const orderCard = document.createElement('div');
                orderCard.className = 'glass rounded-2xl p-6 order-card';

                // 设置过期订单的样式
                if (groupRemainingDaysText === '已过期') {
                    orderCard.classList.add('opacity-60');
                }

                // 确定状态指示器类名
                let statusClass = 'status-pending';
                if (groupRemainingDaysText === '已过期') {
                    statusClass = 'status-expired';
                } else if (groupRemainingDaysText === '永久有效' || parseInt(groupRemainingDaysText) > 7) {
                    statusClass = 'status-active';
                }

                orderCard.innerHTML = `
                    <div class="flex items-start space-x-4">
                        <div class="relative">
                            <img src="${groupAvatarUrl}" alt="群头像"
                                class="h-14 w-14 rounded-2xl object-cover shadow-lg border-2 border-white/20"
                                onerror="this.onerror=null;this.src='https://placehold.co/56x56/667eea/ffffff?text=群';">
                            <div class="absolute -bottom-1 -right-1 w-5 h-5 glass rounded-full flex items-center justify-center">
                                <i data-lucide="users" class="w-3 h-3 text-white"></i>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex justify-between items-start mb-3">
                                <h4 class="font-semibold text-white text-lg truncate">群 ${escapeHtml(group.groupNumber)}</h4>
                                <span class="px-3 py-1 glass rounded-xl text-sm font-medium text-white whitespace-nowrap ml-2">
                                    ${groupRemainingDaysText === '已过期' ? '已过期' : groupRemainingDaysText === '永久有效' ? '永久' : `${groupRemainingDaysText}天`}
                                </span>
                            </div>

                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-white/80 text-sm">
                                    <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                                    <span class="status-indicator ${statusClass}">到期时间: ${escapeHtml(groupExpirationText)}</span>
                                </div>
                                <div class="flex items-center text-white/80 text-sm">
                                    <i data-lucide="tag" class="w-4 h-4 mr-2"></i>
                                    <span>档位: ${escapeHtml(skuTypesList)}</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-white/60 text-sm flex items-center">
                                    <i data-lucide="package" class="w-4 h-4 mr-1"></i>
                                    ${group.items.length} 个订单
                                </span>
                                <button class="view-details-button glass rounded-xl px-4 py-2 text-white/90 hover:text-white btn-hover flex items-center space-x-2">
                                    <span>查看详情</span>
                                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加查看详情按钮事件
                const viewDetailsButton = orderCard.querySelector('.view-details-button');
                viewDetailsButton.addEventListener('click', () => {
                    showOrderDetails(group);
                });
                
                userOrdersList.appendChild(orderCard);
            });
            
        } catch (error) {
            noOrdersMessage.textContent = `获取订单失败: ${error.message}`;
            console.error('渲染用户订单列表失败:', error);
        }
    }
    
    // 添加订单刷新功能
    function addRefreshButton() {
        const refreshButton = document.getElementById('refreshOrdersButton');
        if (refreshButton) {
            refreshButton.addEventListener('click', async () => {
                await renderUserOrders();
            });
        }
    }

    // 初始化页面
    async function initPage() {
        if (checkLoginStatus()) {
            showSection('userPanel');
            await renderUserOrders();
            addRefreshButton(); // 添加刷新按钮
        } else {
            showSection('initialView');
        }
    }

    function toggleButtonLoading(button, isLoading) {
        const textSpan = button.querySelector('.button-text');
        const loaderSpan = button.querySelector('.loader');
        const iconSpan = button.querySelector('.button-icon');

        if (isLoading) {
            button.disabled = true;
            button.classList.add('opacity-75', 'cursor-not-allowed');
            if (textSpan) textSpan.classList.add('hidden');
            if (loaderSpan) loaderSpan.classList.remove('hidden');
            if (iconSpan) iconSpan.classList.add('hidden');
        } else {
            button.disabled = false;
            button.classList.remove('opacity-75', 'cursor-not-allowed');
            if (textSpan) textSpan.classList.remove('hidden');
            if (loaderSpan) loaderSpan.classList.add('hidden');
            if (iconSpan) iconSpan.classList.remove('hidden');
        }
    }

    function showSection(sectionId) {
        [initialView, bindSection, loginSection, registerSection, userPanel].forEach(section => {
            if (section && section.id === sectionId) {
                section.classList.add('active');
            } else if (section) {
                section.classList.remove('active');
            }
        });
        
        if (bindOrderNumberInput) bindOrderNumberInput.value = '';
        if (bindGroupNumberInput) bindGroupNumberInput.value = '';
        
        if (document.getElementById('loginUsername')) document.getElementById('loginUsername').value = '';
        if (document.getElementById('loginPassword')) document.getElementById('loginPassword').value = '';
        if (document.getElementById('registerUsername')) document.getElementById('registerUsername').value = '';
        if (document.getElementById('registerEmail')) document.getElementById('registerEmail').value = '';
        if (document.getElementById('registerPassword')) document.getElementById('registerPassword').value = '';
        if (document.getElementById('confirmPassword')) document.getElementById('confirmPassword').value = '';
        
        modalError.classList.add('hidden');
        modalError.textContent = '';
    }

    function showModal(title, contentHtml, errorMessage = '') {
        modalTitle.textContent = title;
        modalBody.innerHTML = contentHtml;
        if (errorMessage) {
            modalError.textContent = errorMessage;
            modalError.classList.remove('hidden');
        } else {
            modalError.classList.add('hidden');
            modalError.textContent = '';
        }
        modal.classList.remove('hidden');
        modal.classList.add('opacity-100');
    }

    function hideModal() {
        modal.classList.add('hidden');
        modal.classList.remove('opacity-100');
        modalBody.innerHTML = '';
        modalError.textContent = '';
        modalError.classList.add('hidden');
    }
    
    // 登录功能
    loginForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        const username = document.getElementById('loginUsername').value.trim();
        const password = document.getElementById('loginPassword').value.trim();
        
        if (!username || !password) {
            showModal('输入错误', '', '用户名和密码均不能为空！');
            return;
        }
        
        toggleButtonLoading(loginButton, true);
        
        try {
            const response = await fetch('/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password })
            });
            const result = await response.json();
            
            if (response.ok && result.success) {
                // 保存用户信息到本地存储
                localStorage.setItem('user', JSON.stringify({
                    username: result.data.username,
                    email: result.data.email,
                    token: result.data.token,
                    registerTime: result.data.registerTime
                }));
                
                showModal('登录成功', '<p>您已成功登录，即将跳转到用户中心。</p>');
                setTimeout(() => {
                    hideModal();
                    initPage();
                }, 1500);
            } else {
                showModal('登录失败', '', result.message || '用户名或密码错误');
            }
        } catch (error) {
            console.error('登录时发生错误:', error);
            showModal('网络错误', '', '登录请求失败，请检查您的网络连接或稍后再试。');
        } finally {
            toggleButtonLoading(loginButton, false);
        }
    });
    
    // 注册功能
    registerForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        const username = document.getElementById('registerUsername').value.trim();
        const email = document.getElementById('registerEmail').value.trim();
        const password = document.getElementById('registerPassword').value.trim();
        const confirmPassword = document.getElementById('confirmPassword').value.trim();
        
        if (!username || !email || !password || !confirmPassword) {
            showModal('输入错误', '', '所有字段均为必填项！');
            return;
        }
        
        if (password !== confirmPassword) {
            showModal('输入错误', '', '两次输入的密码不一致！');
            return;
        }
        
        toggleButtonLoading(registerButton, true);
        
        try {
            const response = await fetch('/register', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, email, password })
            });
            const result = await response.json();
            
            if (response.ok && result.success) {
                showModal('注册成功', '<p>账户创建成功！请使用您的新账户登录。</p>');
                setTimeout(() => {
                    hideModal();
                    showSection('loginSection');
                }, 1500);
            } else {
                showModal('注册失败', '', result.message || '注册失败，请稍后再试。');
            }
        } catch (error) {
            console.error('注册时发生错误:', error);
            showModal('网络错误', '', '注册请求失败，请检查您的网络连接或稍后再试。');
        } finally {
            toggleButtonLoading(registerButton, false);
        }
    });
    
    // 退出登录功能
    logoutButton.addEventListener('click', () => {
        localStorage.removeItem('user');
        showModal('退出成功', '<p>您已成功退出登录。</p>');
        setTimeout(() => {
            hideModal();
            showSection('initialView');
        }, 1500);
    });

    showLoginButton.addEventListener('click', () => showSection('loginSection'));
    showRegisterButton.addEventListener('click', () => showSection('registerSection'));
    backFromBindButton.addEventListener('click', () => showSection('userPanel'));
    backFromLoginButton.addEventListener('click', () => showSection('initialView'));
    backFromRegisterButton.addEventListener('click', () => showSection('initialView'));
    
    switchToLoginLink.addEventListener('click', (e) => {
        e.preventDefault();
        showSection('loginSection');
    });
    
    switchToRegisterLink.addEventListener('click', (e) => {
        e.preventDefault();
        showSection('registerSection');
    });
    
    userPanelBindButton.addEventListener('click', () => showSection('bindSection'));

    closeModalButton.addEventListener('click', hideModal);
    modalOkButton.addEventListener('click', hideModal);
    modal.addEventListener('click', (event) => {
        if (event.target === modal) {
            hideModal();
        }
    });

    bindForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        const orderNumber = bindOrderNumberInput.value.trim();
        const groupNumber = bindGroupNumberInput.value.trim();

        if (!orderNumber || !groupNumber) {
            showModal('输入错误', '', '订单号和绑定群号均不能为空！');
            return;
        }
        toggleButtonLoading(bindButton, true);

        try {
            // 获取用户token
            const user = JSON.parse(localStorage.getItem('user'));
            const token = user ? user.token : null;
            
            if (!token) {
                showModal('未登录', '', '您需要先登录才能绑定订单');
                setTimeout(() => {
                    hideModal();
                    showSection('loginSection');
                }, 1500);
                return;
            }
            
            const response = await fetch('/bind', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}` // 添加授权头
                },
                body: JSON.stringify({ orderNumber, groupNumber })
            });
            const result = await response.json();

            if (response.ok && result.success) {
                const content = `
                    <p><strong>订单号:</strong> ${escapeHtml(result.data.orderNumber)}</p>
                    <p><strong>绑定群号:</strong> ${escapeHtml(result.data.groupNumber)}</p>
                    <p><strong>档位类型:</strong> ${escapeHtml(result.data.skuType)}</p>
                    <p><strong>到期时间:</strong> ${escapeHtml(result.data.expirationDate)}</p>
                `;
                showModal('🎉 绑定成功', content);
                bindForm.reset();
                
                // 绑定成功后刷新订单列表
                setTimeout(() => {
                    hideModal();
                    showSection('userPanel');
                    renderUserOrders();
                }, 1500);
            } else {
                showModal('绑定失败', '', result.message || `服务请求错误 (状态: ${response.status})`);
            }
        } catch (error) {
            console.error('绑定订单时发生错误:', error);
            showModal('网络错误', '', '绑定请求失败，请检查您的网络连接或稍后再试。');
        } finally {
            toggleButtonLoading(bindButton, false);
        }
    });

    function escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') return unsafe;
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function () {
        // 初始化Lucide图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        initPage();
    });
</script>

</body>

</html>
