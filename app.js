const crypto = require('crypto');
const express = require('express');
const path = require('path');
const { body, validationResult } = require('express-validator');
const jwt = require('jsonwebtoken');
const bcryptjs = require('bcryptjs');
const sqlite3 = require('sqlite3').verbose();
const { open } = require('sqlite');
require('dotenv').config();

// --- 配置 ---
const USER_ID = process.env.AFDIAN_USER_ID || '5493af98a6df11ed872052540025c377';
const TOKEN = process.env.AFDIAN_TOKEN || 'aNTXwSJHhMu9KPtefRkAGEpq5Y3gr8Wx';
const DB_PATH = process.env.DB_PATH || './database.sqlite';
const PORT = process.env.PORT || 56203;
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'Ab740228'; // 后台管理密码
const JWT_SECRET = process.env.JWT_SECRET || '2002863371'; // JWT密钥，应该在生产环境中设置为强随机值

if (!USER_ID || !TOKEN || !ADMIN_PASSWORD) {
    console.error('致命错误: 缺少关键配置 (AFDIAN_USER_ID, AFDIAN_TOKEN, ADMIN_PASSWORD)。请检查您的 .env 文件或环境变量。');
    process.exit(1);
}

const SKUID_15 = '6b9d46742cc311f094855254001e7c00';
const SKUID_20 = '590a339e2cbb11f0983452540025c377';
// --- 新增：定义SKU价格用于计算 ---
const PRICE_15 = 15; // SKU_15 的价格，单位：元/月
const PRICE_20 = 20; // SKU_20 的价格，单位：元/月

const AFDIAN_API_URL = "https://afdian.com/api/open/query-order";

const logger = {
    info: (message, meta) => console.log(`[INFO] ${message}`, meta || ''),
    warn: (message, meta) => console.warn(`[WARN] ${message}`, meta || ''),
    error: (message, meta) => console.error(`[ERROR] ${message}`, meta || (meta instanceof Error ? meta.stack : '')),
};

// 数据库连接
let db; 

// 初始化SQLite数据库
async function initializeDatabase() {
  try {
    db = await open({
      filename: DB_PATH,
      driver: sqlite3.Database
    });
    
    logger.info('SQLite 数据库连接已建立。');
    
    // 创建必要的表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        username TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        register_time TEXT NOT NULL
      );
      
      CREATE TABLE IF NOT EXISTS bindings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT NOT NULL,
        sku_id TEXT NOT NULL,
        group_number TEXT NOT NULL,
        owner TEXT NOT NULL DEFAULT 'admin',
        bind_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        expiration_time TEXT,             -- 绝对到期时间
        original_duration_months INTEGER, -- 订单原始购买时长（月）
        remaining_days INTEGER,           -- 新增：剩余天数
        is_active INTEGER DEFAULT 0,      -- 新增：标记订单是否正在计费（0=待激活，1=激活中）
        UNIQUE(order_number, sku_id)
      );
      
      CREATE INDEX IF NOT EXISTS idx_bindings_owner ON bindings(owner);
      CREATE INDEX IF NOT EXISTS idx_bindings_expiration ON bindings(expiration_time);
      CREATE INDEX IF NOT EXISTS idx_bindings_group ON bindings(group_number);
    `);
    
    // 为现有表添加新列（如果不存在）
    try {
      await db.exec(`
        PRAGMA foreign_keys=off;
        
        BEGIN TRANSACTION;
        
        -- 添加remaining_days列（如果不存在）
        ALTER TABLE bindings ADD COLUMN remaining_days INTEGER DEFAULT NULL;
        
        -- 添加is_active列（如果不存在）
        ALTER TABLE bindings ADD COLUMN is_active INTEGER DEFAULT 0;
        
        COMMIT;
        
        PRAGMA foreign_keys=on;
      `);
      logger.info('已添加新列到bindings表。');
    } catch (alterError) {
      // 列可能已经存在，这是正常的
      logger.info('添加新列时出现警告（可能是列已存在）:', alterError.message);
    }
    
    logger.info('SQLite 数据库表结构已初始化。');
    return true;
  } catch (err) {
    logger.error('初始化 SQLite 数据库失败:', err);
    return false;
  }
}

// 新增：迁移现有绑定数据到新的剩余天数模式
async function migrateExistingBindings() {
  try {
    logger.info('开始迁移现有订单数据到新的剩余天数模式');
    await db.run('BEGIN TRANSACTION');
    
    // 获取所有没有remaining_days的绑定
    const bindings = await db.all(`
      SELECT id, order_number, group_number, expiration_time, original_duration_months
      FROM bindings
      WHERE remaining_days IS NULL
    `);
    
    if (bindings.length === 0) {
      logger.info('没有需要迁移的订单数据');
      await db.run('COMMIT');
      return;
    }
    
    const now = new Date();
    const groupActiveMap = {}; // 用于跟踪每个群组中已经激活的订单
    
    for (const binding of bindings) {
      let remainingDays = 0;
      
      if (binding.expiration_time) {
        const expirationDate = new Date(binding.expiration_time);
        if (expirationDate > now) {
          // 计算剩余天数
          const diffMs = expirationDate.getTime() - now.getTime();
          remainingDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
        }
      } else {
        // 永久有效的订单
        remainingDays = -1; // 使用-1表示永久
      }
      
      // 确定是否激活
      const groupNumber = binding.group_number;
      let isActive = 0;
      
      if (!groupActiveMap[groupNumber]) {
        // 如果该群组还没有激活的订单，将这个设为激活
        isActive = 1;
        groupActiveMap[groupNumber] = true;
      }
      
      // 更新订单
      await db.run(
        'UPDATE bindings SET remaining_days = ?, is_active = ? WHERE id = ?',
        [remainingDays, isActive, binding.id]
      );
      
      logger.info(`已迁移订单 ID ${binding.id}, 订单号 ${binding.order_number}, 剩余天数: ${remainingDays}, 激活状态: ${isActive}`);
    }
    
    await db.run('COMMIT');
    logger.info(`订单数据迁移完成，共迁移 ${bindings.length} 条记录`);
  } catch (error) {
    await db.run('ROLLBACK').catch(e => logger.error('回滚事务失败:', e));
    logger.error('迁移订单数据时出错:', error);
  }
}

// 新增：续费合并逻辑 - 将新订单时间合并到现有订单
async function mergeRenewalOrder(groupNumber, skuId, newRemainingDays, orderNumber, owner) {
  try {
    // 查找该群组中相同SKU的现有未到期订单（优先选择激活的订单）
    const existingOrder = await db.get(`
      SELECT id, remaining_days, is_active, order_number
      FROM bindings
      WHERE group_number = ? AND sku_id = ? AND
            (remaining_days > 0 OR remaining_days = -1)
      ORDER BY is_active DESC, bind_time ASC
      LIMIT 1
    `, [groupNumber, skuId]);

    if (existingOrder) {
      // 如果找到现有订单，合并时间
      let newTotalDays;
      if (existingOrder.remaining_days === -1) {
        // 如果现有订单是永久的，保持永久
        newTotalDays = -1;
        logger.info(`续费合并: 群组 ${groupNumber} 已有永久订单，新订单 ${orderNumber} 的时间将被忽略`);
      } else {
        // 合并剩余天数
        newTotalDays = existingOrder.remaining_days + newRemainingDays;
        logger.info(`续费合并: 群组 ${groupNumber} 订单 ${existingOrder.order_number} 剩余天数从 ${existingOrder.remaining_days} 增加到 ${newTotalDays}`);
      }

      // 更新现有订单的剩余天数，并在订单号中标记续费
      const updatedOrderNumber = existingOrder.order_number.includes('RENEWED')
        ? existingOrder.order_number
        : `${existingOrder.order_number}-RENEWED-${orderNumber}`;

      await db.run(`
        UPDATE bindings
        SET remaining_days = ?, order_number = ?
        WHERE id = ?
      `, [newTotalDays, updatedOrderNumber, existingOrder.id]);

      return {
        merged: true,
        existingOrderId: existingOrder.id,
        newTotalDays: newTotalDays,
        message: `续费成功！已将 ${newRemainingDays} 天合并到现有订单，总剩余天数: ${newTotalDays === -1 ? '永久' : newTotalDays} 天`
      };
    }

    return { merged: false };
  } catch (error) {
    logger.error(`续费合并过程中出错:`, error);
    throw error;
  }
}

// 新增：管理群组内订单激活状态的函数（优化版 - 支持队列消费）
async function manageGroupActivation(groupNumber) {
  try {
    await db.run('BEGIN TRANSACTION');

    // 查询该群组中所有有效的订单，按绑定时间排序
    const groupBindings = await db.all(`
      SELECT id, order_number, expiration_time, is_active, remaining_days, bind_time
      FROM bindings
      WHERE group_number = ?
      AND (
        expiration_time IS NULL
        OR (remaining_days IS NOT NULL AND remaining_days > 0)
        OR remaining_days = -1
        OR expiration_time > datetime('now')
      )
      ORDER BY bind_time ASC
    `, [groupNumber]);

    if (groupBindings.length === 0) {
      await db.run('COMMIT');
      return; // 没有有效订单，直接返回
    }

    // 检查是否有永久有效的订单
    const permanentBinding = groupBindings.find(b => b.remaining_days === -1 || b.expiration_time === null);
    if (permanentBinding) {
      // 如果有永久有效的订单，则将其标记为激活，其他订单标记为非激活
      await db.run(`UPDATE bindings SET is_active = 0 WHERE group_number = ?`, [groupNumber]);
      await db.run(`UPDATE bindings SET is_active = 1 WHERE id = ?`, [permanentBinding.id]);
      await db.run('COMMIT');
      logger.info(`群组 ${groupNumber} 有永久有效订单 ${permanentBinding.order_number}，已将其设为激活状态`);
      return;
    }

    // 查找当前激活的订单
    const activeBindings = groupBindings.filter(b => b.is_active === 1);

    if (activeBindings.length === 0) {
      // 如果没有激活的订单，激活第一个有效订单
      const firstValidBinding = groupBindings.find(b => b.remaining_days > 0 || b.remaining_days === -1);
      if (firstValidBinding) {
        await db.run(`UPDATE bindings SET is_active = 1 WHERE id = ?`, [firstValidBinding.id]);
        logger.info(`群组 ${groupNumber} 没有激活的订单，已激活第一个有效订单 ID ${firstValidBinding.id} (${firstValidBinding.order_number})`);
      }
    } else if (activeBindings.length > 1) {
      // 如果有多个激活的订单，只保留最早的一个（队列消费机制）
      const primaryActive = activeBindings[0];
      for (let i = 1; i < activeBindings.length; i++) {
        await db.run(`UPDATE bindings SET is_active = 0 WHERE id = ?`, [activeBindings[i].id]);
        logger.info(`群组 ${groupNumber} 停用重复激活的订单 ID ${activeBindings[i].id} (${activeBindings[i].order_number})`);
      }
      logger.info(`群组 ${groupNumber} 保持订单 ID ${primaryActive.id} (${primaryActive.order_number}) 为激活状态`);
    }

    // 检查当前激活订单是否已用完，如果是则激活下一个
    const currentActive = activeBindings.length > 0 ? activeBindings[0] : null;
    if (currentActive && currentActive.remaining_days === 0) {
      // 当前激活订单已用完，停用它并激活下一个
      await db.run(`UPDATE bindings SET is_active = 0 WHERE id = ?`, [currentActive.id]);
      logger.info(`群组 ${groupNumber} 订单 ID ${currentActive.id} (${currentActive.order_number}) 已用完，已停用`);

      // 找到下一个有效订单（按绑定时间排序）
      const nextBinding = groupBindings.find(b =>
        b.id !== currentActive.id &&
        (b.remaining_days > 0 || b.remaining_days === -1) &&
        b.is_active === 0
      );

      if (nextBinding) {
        await db.run(`UPDATE bindings SET is_active = 1 WHERE id = ?`, [nextBinding.id]);
        logger.info(`群组 ${groupNumber} 已激活下一个订单 ID ${nextBinding.id} (${nextBinding.order_number})`);
      } else {
        logger.info(`群组 ${groupNumber} 没有更多可激活的订单`);
      }
    }

    await db.run('COMMIT');
    logger.info(`已更新群组 ${groupNumber} 的订单激活状态`);
  } catch (error) {
    await db.run('ROLLBACK').catch(e => logger.error('回滚事务失败:', e));
    logger.error(`管理群组 ${groupNumber} 的订单激活状态时出错:`, error);
    throw error;
  }
}

// 新增：更新剩余天数的定时任务
async function updateRemainingDays() {
  try {
    logger.info('开始执行每日剩余天数更新任务');
    await db.run('BEGIN TRANSACTION');
    
    // 查找所有激活状态的非永久订单
    const activeBindings = await db.all(`
      SELECT id, group_number, remaining_days
      FROM bindings
      WHERE is_active = 1 AND remaining_days IS NOT NULL AND remaining_days > 0
    `);
    
    logger.info(`找到 ${activeBindings.length} 个需要更新的激活订单`);
    
    for (const binding of activeBindings) {
      // 减少一天剩余天数
      const newRemainingDays = binding.remaining_days - 1;
      
      if (newRemainingDays <= 0) {
        // 订单已用完，需要更新激活状态并激活下一个订单
        await db.run('UPDATE bindings SET remaining_days = 0, is_active = 0 WHERE id = ?', binding.id);
        logger.info(`订单 ID ${binding.id} 剩余天数已用完，已停止激活`);
        
        // 找出该群组中下一个待激活的订单（按绑定时间排序）
        const nextBinding = await db.get(`
          SELECT id 
          FROM bindings 
          WHERE group_number = ? AND id != ? AND 
                ((expiration_time IS NULL) OR (remaining_days IS NOT NULL AND remaining_days > 0)) AND 
                is_active = 0
          ORDER BY bind_time ASC
          LIMIT 1
        `, [binding.group_number, binding.id]);
        
        if (nextBinding) {
          // 激活下一个订单
          await db.run('UPDATE bindings SET is_active = 1 WHERE id = ?', nextBinding.id);
          logger.info(`已自动激活群组 ${binding.group_number} 的下一个订单 ID ${nextBinding.id}`);
        }
      } else {
        // 简单减少一天
        await db.run('UPDATE bindings SET remaining_days = ? WHERE id = ?', [newRemainingDays, binding.id]);
        logger.info(`已更新订单 ID ${binding.id} 的剩余天数为 ${newRemainingDays}`);
      }
    }
    
    await db.run('COMMIT');
    logger.info('每日剩余天数更新任务完成');
  } catch (error) {
    await db.run('ROLLBACK').catch(e => logger.error('回滚事务失败:', e));
    logger.error('更新剩余天数时出错:', error);
  }
}

// 设置每天凌晨执行的定时任务
function setupDailyTask() {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);
  
  const timeUntilMidnight = tomorrow.getTime() - now.getTime();
  
  // 设置第一次执行的定时器
  setTimeout(() => {
    updateRemainingDays();
    
    // 之后每24小时执行一次
    setInterval(updateRemainingDays, 24 * 60 * 60 * 1000);
  }, timeUntilMidnight);
  
  logger.info(`定时任务已设置，将在 ${new Date(now.getTime() + timeUntilMidnight).toLocaleString()} 首次执行`);
}

// 修改：计算群组剩余天数的函数
function calculateGroupRemainingDays(groupBindings) {
  // 如果有永久绑定，直接返回永久
  if (groupBindings.some(b => !b.expiration_time)) {
    return { 
      totalDays: Infinity, 
      hasPermanent: true,
      activeOrderId: groupBindings.find(b => b.is_active === 1)?.id || null
    };
  }

  // 计算所有订单的剩余天数总和
  let totalRemainingDays = 0;
  groupBindings.forEach(binding => {
    // 使用remaining_days字段，如果不存在则根据expiration_time计算
    if (binding.remaining_days !== null && binding.remaining_days !== undefined) {
      totalRemainingDays += binding.remaining_days;
    } else if (binding.expiration_time) {
      const now = new Date();
      const expirationDate = new Date(binding.expiration_time);
      if (expirationDate > now) {
        const diffMs = expirationDate.getTime() - now.getTime();
        const days = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
        totalRemainingDays += days;
      }
    }
  });

  // 找到当前激活的订单
  const activeBinding = groupBindings.find(b => b.is_active === 1);

  return {
    totalDays: totalRemainingDays,
    hasPermanent: false,
    activeOrderId: activeBinding?.id || null,
    // 如果有激活的订单，返回其剩余天数
    activeRemainingDays: activeBinding ? (activeBinding.remaining_days || 0) : 0
  };
}

function getSkuType(skuId) {
    if (skuId === SKUID_15) return '15元档位';
    if (skuId === SKUID_20) return '20元档位';
    logger.warn(`遇到未知的 SKU ID: ${skuId}`);
    return '未知档位';
}

function generateSignature(params, ts, userId, token) {
    if (!params || typeof params !== 'object' || !ts || !userId || !token) {
        logger.error('generateSignature: 无效的输入参数', { params, ts, userId: userId ? '***' : null, token: token ? '***' : null });
        throw new Error('生成签名失败：输入参数无效。');
    }
    try {
        const paramsString = JSON.stringify(params);
        const signParams = { params: paramsString, ts, user_id: userId };
        const kvString = Object.keys(signParams)
            .sort()
            .map(key => `${key}${signParams[key]}`)
            .join('');
        return crypto.createHash('md5').update(token + kvString).digest('hex');
    } catch (error) {
        logger.error('生成签名时出错:', error);
        throw new Error(`生成签名过程中发生错误: ${error.message}`);
    }
}

async function afdianApiRequest({ params, apiUrl }) {
    if (!params || typeof params !== 'object' || !apiUrl || typeof apiUrl !== 'string') {
        logger.error('afdianApiRequest: 无效的输入参数', { params, apiUrl });
        throw new Error('API 请求失败：输入参数无效。');
    }
    const ts = Math.floor(Date.now() / 1000);
    let sign;
    try {
        sign = generateSignature(params, ts, USER_ID, TOKEN);
    } catch (signError) {
        logger.error('为 API 请求生成签名失败', signError);
        throw new Error(`API 请求失败 (签名错误): ${signError.message}`);
    }
    const requestBody = { user_id: USER_ID, params: JSON.stringify(params), ts, sign };
    try {
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
            const errorBody = await response.text();
            const errorMessage = `爱发电 API HTTP 错误! 状态: ${response.status}, 响应体: ${errorBody}`;
            logger.error(`请求 ${apiUrl} 时发生爱发电 API HTTP 错误`, { status: response.status, body: errorBody, requestBody });
            throw new Error(errorMessage);
        }
        const data = await response.json();
        if (data.ec !== 200) {
            const apiErrorMessage = `爱发电 API 业务错误: ${data.em || '未知错误'} (代码: ${data.ec})`;
            logger.error(`请求 ${apiUrl} 时发生爱发电 API 业务错误`, { code: data.ec, message: data.em, requestBody, responseData: data });
            throw new Error(apiErrorMessage);
        }
        if (apiUrl.includes('query-order') && (!data.data || !Array.isArray(data.data.list))) {
            const missingDataError = "爱发电 API 响应缺少预期的 data.list 字段。";
            logger.error(missingDataError, { response: data, requestBody });
            throw new Error(missingDataError);
        } else if (!data.data && apiUrl.includes('query-order')) {
            logger.warn(`请求 ${apiUrl} 时爱发电 API 响应缺少 data 字段，但 ec 是 200。可能表示无匹配订单。`, { response: data, requestBody });
        }
        return data;
    } catch (error) {
        logger.error(`请求爱发电 API ${apiUrl} 过程中出错:`, error);
        throw new Error(`爱发电 API 请求失败: ${error.message}`);
    }
}

async function fetchOrderByTradeNo(orderNumber) {
    if (typeof orderNumber !== 'string' || !orderNumber) {
        logger.warn('fetchOrderByTradeNo: 提供了无效的订单号。', { orderNumber });
        return null;
    }
    try {
        logger.info(`正在从 API 获取单个订单，out_trade_no: ${orderNumber}`);
        const response = await afdianApiRequest({
            params: { out_trade_no: orderNumber },
            apiUrl: AFDIAN_API_URL
        });
        if (response.data && response.data.list && response.data.list.length > 0) {
            logger.info(`在 API 响应中找到订单 ${orderNumber}。`);
            return response.data.list[0];
        } else {
            logger.info(`在 API 响应中未找到订单 ${orderNumber}。`);
            return null;
        }
    } catch (error) {
        logger.error(`通过交易号 ${orderNumber} 获取订单时出错:`, error);
        return null;
    }
}

function checkDatabaseConnection(req, res, next) {
    if (!db) {
        logger.error('请求已收到，但数据库未连接。');
        return res.status(503).json({ success: false, message: '服务暂时不可用 (数据库连接失败)，请稍后重试。' });
    }
    next();
}

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// --- 用户认证中间件 ---
async function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.status(401).json({ success: false, message: '未授权访问，请先登录' });
    }
    
    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        
        // 检查用户是否存在
        const user = await db.get('SELECT username, email FROM users WHERE username = ?', decoded.username);
        
        if (!user) {
            return res.status(401).json({ success: false, message: '用户不存在或已被删除' });
        }
        
        req.user = decoded;
        next();
    } catch (error) {
        logger.error('JWT验证失败:', error);
        return res.status(403).json({ success: false, message: '无效或过期的令牌，请重新登录' });
    }
}

function authenticateAdmin(req, res, next) {
    const password = req.headers['x-admin-password'] || req.body.password;
    if (!password || password !== ADMIN_PASSWORD) {
        logger.warn('后台管理身份验证失败。');
        return res.status(401).json({ success: false, message: '未授权。请提供正确的管理员密码。' });
    }
    next();
}

// --- 用户注册和登录 API ---
app.post('/register', checkDatabaseConnection, [
    body('username').trim().isLength({ min: 3, max: 20 }).withMessage('用户名长度必须在3到20个字符之间'),
    body('email').trim().isEmail().withMessage('请提供有效的电子邮箱地址'),
    body('password').isLength({ min: 6 }).withMessage('密码长度必须至少为6个字符')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: errors.array().map(e => e.msg).join('; ') });
    }
    
    const { username, email, password } = req.body;
    
    try {
        // 检查用户名是否已存在
        const existingUser = await db.get('SELECT username FROM users WHERE username = ?', username);
        
        if (existingUser) {
            return res.status(409).json({ success: false, message: '用户名已被使用' });
        }
        
        // 检查邮箱是否已存在
        const existingEmail = await db.get('SELECT email FROM users WHERE email = ?', email);
        
        if (existingEmail) {
            return res.status(409).json({ success: false, message: '该邮箱已被注册' });
        }
        
        // 密码加密
        const salt = await bcryptjs.genSalt(10);
        const hashedPassword = await bcryptjs.hash(password, salt);
        const registerTime = new Date().toISOString();
        
        // 创建用户记录
        await db.run(
            'INSERT INTO users (username, email, password, register_time) VALUES (?, ?, ?, ?)',
            [username, email, hashedPassword, registerTime]
        );
        
        logger.info(`新用户注册成功: ${username}`);
        
        res.json({
            success: true,
            message: '注册成功',
            data: {
                username,
                email,
                registerTime
            }
        });
    } catch (error) {
        logger.error('用户注册过程中出错:', error);
        res.status(500).json({ success: false, message: `注册失败: ${error.message}` });
    }
});

app.post('/login', checkDatabaseConnection, [
    body('username').trim().notEmpty().withMessage('用户名不能为空'),
    body('password').notEmpty().withMessage('密码不能为空')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: errors.array().map(e => e.msg).join('; ') });
    }
    
    const { username, password } = req.body;
    
    try {
        // 获取用户信息
        const user = await db.get('SELECT * FROM users WHERE username = ?', username);
        
        if (!user) {
            return res.status(401).json({ success: false, message: '用户名或密码错误' });
        }
        
        // 验证密码
        const validPassword = await bcryptjs.compare(password, user.password);
        if (!validPassword) {
            return res.status(401).json({ success: false, message: '用户名或密码错误' });
        }
        
        // 生成JWT令牌
        const token = jwt.sign(
            { username: user.username, email: user.email },
            JWT_SECRET,
            { expiresIn: '7d' }
        );
        
        logger.info(`用户登录成功: ${username}`);
        
        res.json({
            success: true,
            message: '登录成功',
            data: {
                username: user.username,
                email: user.email,
                token,
                registerTime: user.register_time
            }
        });
    } catch (error) {
        logger.error('用户登录过程中出错:', error);
        res.status(500).json({ success: false, message: `登录失败: ${error.message}` });
    }
});

// --- 绑定调试接口 ---
app.get('/debug/binding/:orderNumber', authenticateToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        
        // 查找绑定
        const binding = await db.get(
            'SELECT * FROM bindings WHERE order_number = ?',
            orderNumber
        );
        
        if (!binding) {
            return res.status(404).json({
                success: false,
                message: `未找到订单 ${orderNumber} 的绑定信息`
            });
        }
        
        // 获取所有相关信息
        const skuType = getSkuType(binding.sku_id);
        let expirationDate = '未知';
        
        if (binding.expiration_time) {
            const expirationTime = new Date(binding.expiration_time);
            if (!isNaN(expirationTime.getTime())) {
                expirationDate = expirationTime.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
            }
        } else {
            expirationDate = '永久有效';
        }
        
        // 提供完整的调试信息
        res.json({
            success: true,
            message: '绑定诊断信息',
            data: {
                orderNumber,
                skuId: binding.sku_id,
                expectedSkuId_20: SKUID_20,
                expectedSkuId_15: SKUID_15,
                skuIdMatches: binding.sku_id === SKUID_15 || binding.sku_id === SKUID_20,
                skuType,
                groupNumber: binding.group_number,
                expirationDate,
                remainingDays: binding.remaining_days,
                isActive: binding.is_active === 1,
                owner: binding.owner,
                originalDurationMonths: binding.original_duration_months
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: `诊断失败: ${error.message}`
        });
    }
});

// --- 用户订单历史记录 API ---
app.get('/user/bindings', authenticateToken, checkDatabaseConnection, async (req, res) => {
    try {
        logger.info(`收到获取用户当前有效绑定请求: 用户: ${req.user.username}`);
        
        // 获取当前活跃的绑定信息（只获取属于当前用户的）
        const userBindings = await db.all(`
            SELECT order_number, sku_id, group_number, bind_time, 
                   expiration_time, original_duration_months, remaining_days, is_active
            FROM bindings 
            WHERE owner = ?
            ORDER BY bind_time DESC
        `, [req.user.username]);
        
        // 格式化绑定信息
        const formattedBindings = userBindings.map(binding => {
            let expirationDateDisplay;
            
            if (binding.expiration_time) {
                const expirationDate = new Date(binding.expiration_time);
                const now = new Date();
                if (expirationDate > now) {
                    expirationDateDisplay = expirationDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
                } else {
                    expirationDateDisplay = '已过期';
                }
            } else {
                expirationDateDisplay = '永久有效';
            }
            
            // 使用remaining_days而不是计算ttl
            const remainingDays = binding.remaining_days !== null ? binding.remaining_days : (binding.expiration_time ? -2 : -1); // -1 表示永久，-2 表示未知
            
            return {
                orderNumber: binding.order_number,
                groupNumber: binding.group_number,
                skuId: binding.sku_id,
                skuType: getSkuType(binding.sku_id),
                expirationDate: expirationDateDisplay,
                expirationISOString: binding.expiration_time,
                bindTime: binding.bind_time,
                remainingDays: remainingDays,
                isActive: binding.is_active === 1,
                owner: req.user.username,
                originalDurationMonths: binding.original_duration_months
            };
        });
        
        logger.info(`成功获取用户 ${req.user.username} 的绑定, 共 ${formattedBindings.length} 条记录`);
        res.json({
            success: true,
            message: '获取当前有效绑定成功',
            data: formattedBindings
        });
    } catch (error) {
        logger.error('获取用户绑定时出错:', error);
        res.status(500).json({ success: false, message: `获取绑定失败: ${error.message}` });
    }
});

// --- 后台管理 API 端点 ---
app.post('/admin/bindings', authenticateAdmin, checkDatabaseConnection, async (req, res) => {
    try {
        logger.info('正在为后台管理面板从数据库获取所有绑定信息。');
        
        // 获取所有绑定信息
        const bindings = await db.all(`
            SELECT id, order_number, sku_id, group_number, owner, bind_time, 
                   expiration_time, original_duration_months, remaining_days, is_active
            FROM bindings
            ORDER BY bind_time ASC
        `);
        
        // 按群号分组原始数据
        const groupedRawData = {};
        bindings.forEach(binding => {
            if (!groupedRawData[binding.group_number]) {
                groupedRawData[binding.group_number] = [];
            }
            groupedRawData[binding.group_number].push(binding);
        });

        const finalGroupedList = [];
        const now = new Date();

        for (const groupNumber in groupedRawData) {
            const groupBindings = groupedRawData[groupNumber];
            
            // 使用辅助函数计算群组的剩余天数
            const { totalDays, hasPermanent, activeOrderId } = calculateGroupRemainingDays(groupBindings);

            // 准备群组级别的信息
            const groupSkuTypes = new Set(groupBindings.map(b => getSkuType(b.sku_id)));
            const groupOwner = groupBindings[0] ? groupBindings[0].owner : 'admin'; 

            finalGroupedList.push({
                groupNumber: groupNumber,
                items: groupBindings.map(b => ({
                    id: b.id,
                    orderNumber: b.order_number,
                    skuId: b.sku_id,
                    skuType: getSkuType(b.sku_id),
                    expirationDate: b.expiration_time ? 
                        new Date(b.expiration_time).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }) : 
                        '永久',
                    expirationISOString: b.expiration_time,
                    bindTime: b.bind_time,
                    owner: b.owner,
                    remainingDays: b.remaining_days,
                    isActive: b.is_active === 1,
                    originalDurationMonths: b.original_duration_months
                })),
                skuTypes: Array.from(groupSkuTypes), 
                totalRemainingDays: totalDays, // 总剩余天数
                hasPermanent: hasPermanent,
                activeOrderId: activeOrderId
            });
        }

        // 根据剩余天数排序
        finalGroupedList.sort((a, b) => {
            if (a.hasPermanent && !b.hasPermanent) return -1;
            if (!a.hasPermanent && b.hasPermanent) return 1;
            return b.totalRemainingDays - a.totalRemainingDays;
        });
        
        logger.info(`为后台管理面板准备了 ${finalGroupedList.length} 条分组后的绑定条目。`);
        res.json({ success: true, data: finalGroupedList });
    } catch (error) {
        logger.error('为后台管理面板获取绑定信息时出错:', error);
        res.status(500).json({ success: false, message: `获取绑定列表失败: ${error.message}` });
    }
});

// --- 修改：订单升级 API 端点 ---
app.post('/admin/bindings/upgrade', authenticateAdmin, checkDatabaseConnection, [
    body('id').isInt().withMessage('订单ID必须是整数。')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        logger.warn('后台管理升级绑定输入验证失败', { errors: errors.array(), body: req.body });
        return res.status(400).json({ success: false, message: errors.array().map(e => e.msg).join('; ') });
    }

    const { id } = req.body;
    const now = new Date();

    try {
        await db.run('BEGIN TRANSACTION');
        logger.info(`开始处理订单升级事务, ID: ${id}`);

        // 1. 获取并验证原始订单
        const originalOrder = await db.get('SELECT * FROM bindings WHERE id = ?', id);

        if (!originalOrder) {
            await db.run('ROLLBACK');
            logger.warn(`升级失败: 未找到ID为 ${id} 的订单。`);
            return res.status(404).json({ success: false, message: '未找到要升级的订单。' });
        }

        if (originalOrder.sku_id !== SKUID_15) {
            await db.run('ROLLBACK');
            logger.warn(`升级失败: 订单 ${id} 不是 ${getSkuType(SKUID_15)}，无法升级。`);
            return res.status(400).json({ success: false, message: `此订单不是 ${getSkuType(SKUID_15)}，无法升级。` });
        }

        // 获取剩余天数
        let remainingDays = 0;
        
        if (originalOrder.remaining_days !== null && originalOrder.remaining_days !== undefined) {
            // 使用数据库中存储的剩余天数
            remainingDays = originalOrder.remaining_days;
            if (remainingDays <= 0) {
                await db.run('ROLLBACK');
                logger.warn(`升级失败: 订单 ${id} 已没有剩余天数。`);
                return res.status(400).json({ success: false, message: '订单已没有剩余天数，无法升级。' });
            }
        } else if (originalOrder.expiration_time) {
            // 如果没有remaining_days字段，则根据expiration_time计算
            const expirationDate = new Date(originalOrder.expiration_time);
            if (expirationDate <= now) {
                await db.run('ROLLBACK');
                logger.warn(`升级失败: 订单 ${id} 已过期，没有剩余价值。`);
                return res.status(400).json({ success: false, message: '订单已过期，无法升级。' });
            }
            
            // 计算剩余天数
            const diffMs = expirationDate.getTime() - now.getTime();
            remainingDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
        } else {
            // 永久订单
            await db.run('ROLLBACK');
            logger.warn(`升级失败: 订单 ${id} 是永久订单，无法进行价值转换。`);
            return res.status(400).json({ success: false, message: '永久有效的订单不支持升级。' });
        }
        
        // 2. 计算等值转换后的天数 (15元/月 → 20元/月)
        // 转换公式: 剩余天数 * (旧价格/新价格)
        const convertedDays = Math.ceil(remainingDays * (PRICE_15 / PRICE_20));
        logger.info(`订单 ${id} 剩余 ${remainingDays} 天, 按比例转换后为 ${convertedDays} 天`);

        // 3. 创建新的升级后订单记录
        const newOrderNumber = `UPGRADED-${originalOrder.order_number}`;
        const bindTime = now.toISOString();

        await db.run(
            `INSERT INTO bindings (
                order_number, sku_id, group_number, owner, bind_time, 
                expiration_time, original_duration_months, remaining_days, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                newOrderNumber,
                SKUID_20,
                originalOrder.group_number,
                originalOrder.owner,
                bindTime,
                null, // 不再使用过期时间，而是使用剩余天数
                Math.ceil(convertedDays / 30), // 大致换算为月数
                convertedDays,
                originalOrder.is_active // 保持原订单的激活状态
            ]
        );
        logger.info(`已创建升级后的新订单记录，新订单号: ${newOrderNumber}, 剩余天数: ${convertedDays}`);

        // 4. 删除旧的订单记录
        await db.run('DELETE FROM bindings WHERE id = ?', id);
        logger.info(`已删除旧订单记录, ID: ${id}`);

        // 5. 管理群组内订单的激活状态
        await manageGroupActivation(originalOrder.group_number);

        // 6. 提交事务
        await db.run('COMMIT');
        logger.info(`订单升级事务成功提交, ID: ${id}`);

        res.json({
            success: true,
            message: `订单升级成功！已将 ${getSkuType(SKUID_15)} 的剩余 ${remainingDays} 天按比例转换为 ${getSkuType(SKUID_20)} 的 ${convertedDays} 天。`
        });

    } catch (error) {
        // 如果事务中发生任何错误，回滚所有更改
        await db.run('ROLLBACK').catch(rollbackErr => logger.error('回滚事务失败:', rollbackErr));
        logger.error(`订单升级过程中出错 (ID: ${id}):`, error);
        res.status(500).json({ success: false, message: `升级失败: ${error.message}` });
    }
});

app.post('/admin/bindings/add', authenticateAdmin, checkDatabaseConnection, [
    body('orderNumber').trim().notEmpty().withMessage('订单号不能为空。'),
    body('groupNumber').trim().notEmpty().withMessage('绑定群号不能为空。'),
    body('skuId').trim().notEmpty().withMessage('SKU ID 不能为空。'),
    body('isPermanent').isBoolean().withMessage('isPermanent 必须是布尔值。'),
    body('owner').optional().trim(),
    body('expirationTimestamp').optional({ nullable: true, checkFalsy: true })
        .if(body('isPermanent').equals('false'))
        .isISO8601().withMessage('到期时间戳格式无效 (YYYY-MM-DDTHH:MM)。')
        .custom((value) => {
            if (new Date(value) <= new Date()) {
                throw new Error('到期时间必须在未来。');
            }
            return true;
        })
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        logger.warn('后台管理添加绑定输入验证失败', { errors: errors.array(), body: req.body });
        return res.status(400).json({ success: false, message: errors.array().map(e => e.msg).join('; ') });
    }
    const { orderNumber, groupNumber, skuId, isPermanent, expirationTimestamp, owner = 'admin' } = req.body;
    
    try {
        logger.info(`后台管理收到添加绑定请求: 订单: ${orderNumber}, 群组: ${groupNumber}, SKU: ${skuId}, 是否永久: ${isPermanent}, 时间戳: ${expirationTimestamp}, 所有者: ${owner}`);
        
        // 检查是否已存在
        const existingBinding = await db.get(
            'SELECT id FROM bindings WHERE order_number = ? AND sku_id = ?',
            [orderNumber, skuId]
        );
        
        if (existingBinding) {
            logger.warn(`后台管理添加绑定失败: 订单 ${orderNumber} (SKU ${skuId}) 已绑定`);
            return res.status(409).json({ success: false, message: `订单号 ${orderNumber} (${getSkuType(skuId)}) 已绑定。` });
        }
        
        let expirationTime = null;
        let expirationDateDisplay = '永久';
        let originalDurationMonths = null;
        let remainingDays = -1; // 默认为永久

        if (isPermanent === false || isPermanent === 'false') {
            if (!expirationTimestamp) {
                return res.status(400).json({ success: false, message: '非永久绑定必须提供到期时间戳。' });
            }
            expirationTime = new Date(expirationTimestamp).toISOString();
            expirationDateDisplay = new Date(expirationTimestamp).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });

            // 计算原始时长和剩余天数
            const now = new Date();
            const diffMs = new Date(expirationTimestamp).getTime() - now.getTime();
            if (diffMs > 0) {
                remainingDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
                originalDurationMonths = Math.ceil(remainingDays / 30); // 每个月约30天
                if (originalDurationMonths < 1) originalDurationMonths = 1; // 最小为1个月
            } else {
                remainingDays = 0;
                originalDurationMonths = 0; // 已过期或无效
            }
        }
        
        // 检查群组中是否已有激活的订单
        const activeBindingExists = await db.get(`
            SELECT id FROM bindings WHERE group_number = ? AND is_active = 1 LIMIT 1
        `, [groupNumber]);
        
        // 如果群组中没有激活的订单，则该订单设置为激活
        const isActive = !activeBindingExists ? 1 : 0;
        
        const bindTime = new Date().toISOString();
        
        // 保存绑定信息
        await db.run(
            `INSERT INTO bindings (
                order_number, sku_id, group_number, owner, bind_time, 
                expiration_time, original_duration_months, remaining_days, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                orderNumber, 
                skuId, 
                groupNumber, 
                owner, 
                bindTime, 
                expirationTime, 
                originalDurationMonths, 
                remainingDays, 
                isActive
            ]
        );
        
        logger.info(`后台管理已添加绑定: 订单 ${orderNumber} (SKU ${skuId}) 到群组 ${groupNumber}, 剩余天数: ${remainingDays}, 激活状态: ${isActive}, 所有者: ${owner}, 原始时长: ${originalDurationMonths}个月`);
        
        // 如果是永久订单，更新群组内所有订单的激活状态
        if (isPermanent === true || isPermanent === 'true') {
            await manageGroupActivation(groupNumber);
        }
        
        res.json({
            success: true,
            message: '绑定添加成功！',
            data: { 
                orderNumber, 
                groupNumber, 
                skuId, 
                skuType: getSkuType(skuId), 
                expirationDate: expirationDateDisplay, 
                remainingDays,
                isActive: isActive === 1,
                owner, 
                originalDurationMonths 
            }
        });
    } catch (error) {
        logger.error('后台管理添加绑定过程中出错:', error);
        res.status(500).json({ success: false, message: `添加绑定失败: ${error.message}` });
    }
});

app.post('/admin/bindings/update', authenticateAdmin, checkDatabaseConnection, [
    body('originalOrderNumber').trim().notEmpty().withMessage('原始订单号不能为空。'),
    body('originalSkuId').trim().notEmpty().withMessage('原始 SKU ID 不能为空。'),
    body('newOrderNumber').trim().notEmpty().withMessage('新的订单号不能为空。'),
    body('newSkuId').trim().notEmpty().withMessage('新的 SKU ID 不能为空。'),
    body('newGroupNumber').trim().notEmpty().withMessage('新的绑定群号不能为空。'),
    body('isPermanent').isBoolean().withMessage('isPermanent 必须是布尔值。'),
    body('owner').optional().trim(),
    body('expirationTimestamp').optional({ nullable: true, checkFalsy: true })
        .if(body('isPermanent').equals('false'))
        .isISO8601().withMessage('到期时间戳格式无效 (YYYY-MM-DDTHH:MM)。')
        .custom((value) => {
            if (new Date(value) <= new Date()) {
                throw new Error('到期时间必须在未来。');
            }
            return true;
        })
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        logger.warn('后台管理更新绑定输入验证失败', { errors: errors.array(), body: req.body });
        return res.status(400).json({ success: false, message: errors.array().map(e => e.msg).join('; ') });
    }
    const { originalOrderNumber, originalSkuId, newOrderNumber, newSkuId, newGroupNumber, isPermanent, expirationTimestamp, owner } = req.body;
    
    try {
        logger.info(`后台管理收到更新绑定请求: 原订单: ${originalOrderNumber}, 原SKU: ${originalSkuId}, 新订单: ${newOrderNumber}, 新SKU: ${newSkuId}, 新群组: ${newGroupNumber}, 是否永久: ${isPermanent}, 时间戳: ${expirationTimestamp}`);
        
        // 检查原始绑定是否存在
        const existingBinding = await db.get(
            'SELECT id, owner, bind_time, group_number, is_active FROM bindings WHERE order_number = ? AND sku_id = ?',
            [originalOrderNumber, originalSkuId]
        );
        
        if (!existingBinding) {
            logger.warn(`后台管理更新绑定失败: 未找到原始订单 ${originalOrderNumber} (SKU ${originalSkuId}) 的绑定`);
            return res.status(404).json({ success: false, message: `未找到订单号 ${originalOrderNumber} (${getSkuType(originalSkuId)}) 的原始绑定信息。` });
        }
        
        const oldGroupNumber = existingBinding.group_number;
        const oldIsActive = existingBinding.is_active;
        
        // 如果更改了订单号或SKU ID，检查新的绑定是否已存在
        if (originalOrderNumber !== newOrderNumber || originalSkuId !== newSkuId) {
            const existingNewBinding = await db.get(
                'SELECT id FROM bindings WHERE order_number = ? AND sku_id = ? AND id != ?',
                [newOrderNumber, newSkuId, existingBinding.id]
            );
            
            if (existingNewBinding) {
                logger.warn(`后台管理更新绑定失败: 新订单号 ${newOrderNumber} (SKU ${newSkuId}) 已存在绑定`);
                return res.status(409).json({ success: false, message: `新的订单号 ${newOrderNumber} (${getSkuType(newSkuId)}) 的绑定信息已存在，无法覆盖。` });
            }
        }

        let expirationTime = null;
        let expirationDateDisplay = '永久';
        let originalDurationMonths = null;
        let remainingDays = -1; // 默认为永久

        if (isPermanent === false || isPermanent === 'false') {
            if (!expirationTimestamp) {
                return res.status(400).json({ success: false, message: '非永久绑定必须提供到期时间戳。' });
            }
            expirationTime = new Date(expirationTimestamp).toISOString();
            expirationDateDisplay = new Date(expirationTimestamp).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });

            // 计算原始时长和剩余天数
            const bindTimeForDurationCalc = new Date(existingBinding.bind_time || new Date());
            const now = new Date();
            const diffMs = new Date(expirationTimestamp).getTime() - now.getTime();
            if (diffMs > 0) {
                remainingDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
                originalDurationMonths = Math.ceil(remainingDays / 30);
                if (originalDurationMonths < 1) originalDurationMonths = 1;
            } else {
                remainingDays = 0;
                originalDurationMonths = 0;
            }
        }
        
        // 使用提供的所有者或保留原所有者
        const currentOwner = owner || existingBinding.owner;
        
        // 更新绑定信息
        await db.run(
            `UPDATE bindings 
            SET order_number = ?, sku_id = ?, group_number = ?, owner = ?, 
                expiration_time = ?, original_duration_months = ?, remaining_days = ?
            WHERE id = ?`,
            [
                newOrderNumber, 
                newSkuId, 
                newGroupNumber, 
                currentOwner, 
                expirationTime, 
                originalDurationMonths, 
                remainingDays, 
                existingBinding.id
            ]
        );
        
        logger.info(`后台管理已更新绑定: 新订单 ${newOrderNumber} (新SKU ${newSkuId}) 到群组 ${newGroupNumber}, 剩余天数: ${remainingDays}, 所有者: ${currentOwner}, 原始时长: ${originalDurationMonths}个月`);
        
        // 如果群组号发生了变化，需要重新管理两个群组的激活状态
        if (oldGroupNumber !== newGroupNumber) {
            await manageGroupActivation(oldGroupNumber);
            await manageGroupActivation(newGroupNumber);
        } else if (isPermanent === true || isPermanent === 'true') {
            // 如果变成了永久订单，需要更新群组激活状态
            await manageGroupActivation(newGroupNumber);
        }
        
        res.json({
            success: true,
            message: '绑定更新成功！',
            data: { 
                orderNumber: newOrderNumber, 
                groupNumber: newGroupNumber, 
                skuId: newSkuId, 
                skuType: getSkuType(newSkuId), 
                expirationDate: expirationDateDisplay,
                remainingDays,
                isActive: oldIsActive === 1,
                owner: currentOwner,
                originalDurationMonths
            }
        });
    } catch (error) {
        logger.error('后台管理更新绑定过程中出错:', error);
        res.status(500).json({ success: false, message: `更新绑定失败: ${error.message}` });
    }
});

app.post('/admin/bindings/delete', authenticateAdmin, checkDatabaseConnection, [
    body('orderNumber').trim().notEmpty().withMessage('订单号不能为空。'),
    body('skuId').trim().notEmpty().withMessage('SKU ID 不能为空。')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        logger.warn('后台管理删除绑定输入验证失败', { errors: errors.array() });
        return res.status(400).json({ success: false, message: errors.array()[0].msg });
    }
    const { orderNumber, skuId } = req.body;
    
    try {
        logger.info(`后台管理收到删除绑定请求: 订单: ${orderNumber}, SKU: ${skuId}`);
        
        // 在删除前，获取绑定信息
        const binding = await db.get(
            'SELECT id, owner, group_number, is_active FROM bindings WHERE order_number = ? AND sku_id = ?',
            [orderNumber, skuId]
        );
        
        if (!binding) {
            logger.warn(`后台管理删除绑定失败: 未找到订单 ${orderNumber} (SKU ${skuId}) 的绑定`);
            return res.status(404).json({ success: false, message: `未找到订单号 ${orderNumber} (${getSkuType(skuId)}) 的绑定信息。` });
        }
        
        const groupNumber = binding.group_number;
        const wasActive = binding.is_active === 1;
        
        // 删除绑定数据
        await db.run(
            'DELETE FROM bindings WHERE id = ?',
            binding.id
        );
        
        logger.info(`后台管理已删除订单 ${orderNumber} (SKU ${skuId}) 的绑定`);
        
        // 如果删除的是激活中的订单，需要更新群组中的激活状态
        if (wasActive) {
            await manageGroupActivation(groupNumber);
        }
        
        res.json({ 
            success: true, 
            message: '绑定删除成功！', 
            data: { orderNumber, skuId, owner: binding.owner } 
        });
    } catch (error) {
        logger.error('后台管理删除绑定过程中出错:', error);
        res.status(500).json({ success: false, message: `删除绑定失败: ${error.message}` });
    }
});

// --- 用户管理接口 ---
app.get('/admin/users', authenticateAdmin, checkDatabaseConnection, async (req, res) => {
    try {
        logger.info('正在为后台管理面板从数据库获取所有用户信息。');
        
        // 获取所有用户信息，不包括密码
        const users = await db.all(
            `SELECT username, email, register_time FROM users`
        );
        
        // 为每个用户获取绑定数量
        const usersList = await Promise.all(users.map(async (user) => {
            try {
                const bindingsCount = await db.get(
                    'SELECT COUNT(*) as count FROM bindings WHERE owner = ?',
                    user.username
                );
                
                return {
                    ...user,
                    bindingsCount: bindingsCount ? bindingsCount.count : 0
                };
            } catch (error) {
                logger.error(`获取用户 ${user.username} 的绑定数量时出错:`, error);
                return {
                    ...user,
                    bindingsCount: 0
                };
            }
        }));
        
        logger.info(`为后台管理面板准备了 ${usersList.length} 条用户记录。`);
        res.json({ success: true, data: usersList });
    } catch (error) {
        logger.error('获取用户列表时出错:', error);
        res.status(500).json({ success: false, message: `获取用户列表失败: ${error.message}` });
    }
});

// --- 修改：用户端绑定 API 端点 ---
app.post('/bind', authenticateToken, checkDatabaseConnection, [
    body('orderNumber').trim().notEmpty().withMessage('订单号不能为空。').isLength({ min: 5, max: 50 }).withMessage('订单号长度应在5到50个字符之间。'),
    body('groupNumber').trim().notEmpty().withMessage('绑定群号不能为空。').isNumeric().withMessage('群号必须是数字。').isLength({ min: 5, max: 15 }).withMessage('群号长度应在5到15个数字之间。')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        logger.warn('绑定请求输入验证失败', { errors: errors.array(), body: req.body });
        return res.status(400).json({ success: false, message: errors.array().map(e => e.msg).join(' ') });
    }
    const { orderNumber, groupNumber } = req.body;
    try {
        logger.info(`收到绑定请求: 用户: ${req.user.username}, 订单: ${orderNumber}, 群组: ${groupNumber}`);
        const order = await fetchOrderByTradeNo(orderNumber);
        if (!order) {
            logger.warn(`绑定失败: 通过 API 未找到订单 - ${orderNumber}`);
            return res.status(404).json({ success: false, message: `未找到订单号 ${orderNumber}。请检查订单号是否正确，或订单是否已成功支付。` });
        }
        
        // 验证订单信息
        if (!order.sku_detail || !Array.isArray(order.sku_detail) || order.sku_detail.length === 0 || !order.sku_detail[0].sku_id) {
            logger.warn(`绑定失败: 订单 ${orderNumber} 的商品详情 (sku_detail) 无效或缺失。`, { order });
            return res.status(400).json({ success: false, message: `订单 ${orderNumber} 的商品信息不完整，无法绑定。请联系客服确认订单详情。` });
        }
        if (order.status !== 2) {
            logger.warn(`绑定失败: 订单 ${orderNumber} 状态 (${order.status}) 不是已支付。`, { order });
            return res.status(400).json({ success: false, message: `订单 ${orderNumber} 尚未支付或状态异常，无法绑定。请确保订单已支付成功。` });
        }
        
        const skuId = order.sku_detail[0].sku_id;
        logger.info(`处理订单绑定 - 原始SKU ID: "${skuId}"`);
        const normalizedSkuId = skuId.trim();
        logger.info(`规范化后的SKU ID: "${normalizedSkuId}", 长度: ${normalizedSkuId.length}`);
        const skuType = getSkuType(normalizedSkuId);
        
        // 检查是否已绑定
        const existingBinding = await db.get(
            'SELECT id FROM bindings WHERE order_number = ? AND sku_id = ?',
            [orderNumber, normalizedSkuId]
        );
        
        if (existingBinding) {
            logger.warn(`绑定失败: 订单 ${orderNumber} (SKU ${normalizedSkuId}) 已绑定`);
            const binding = await db.get(
                'SELECT group_number FROM bindings WHERE id = ?',
                existingBinding.id
            );
            return res.status(409).json({ success: false, message: `此订单 (${skuType}) 已绑定到群号 ${binding.group_number}。无需重复绑定。` });
        }
        
        // 获取订单购买的月数
        const months = parseInt(order.sku_detail[0].count, 10);
        if (isNaN(months) || months <= 0) {
            logger.warn(`绑定失败: 订单 ${orderNumber} (SKU ${normalizedSkuId}) 的有效期 (${months}个月) 无效。`, { order });
            return res.status(400).json({ success: false, message: `订单 ${orderNumber} (${skuType}) 的购买时长无效，无法绑定。请联系客服。` });
        }
        
        // 计算订单剩余天数 (1个月约等于30天)
        const remainingDays = months * 30;

        // 尝试续费合并：检查是否可以合并到现有订单
        const renewalResult = await mergeRenewalOrder(groupNumber, normalizedSkuId, remainingDays, orderNumber, req.user.username);

        if (renewalResult.merged) {
            // 续费合并成功，返回成功消息
            logger.info(`续费合并成功: 订单 ${orderNumber} 已合并到群组 ${groupNumber} 的现有订单`);
            return res.json({
                success: true,
                message: renewalResult.message,
                data: {
                    orderNumber: orderNumber,
                    skuType: skuType,
                    groupNumber: groupNumber,
                    totalRemainingDays: renewalResult.newTotalDays,
                    merged: true
                }
            });
        }

        // 如果没有合并，继续创建新订单
        logger.info(`未找到可合并的订单，为群组 ${groupNumber} 创建新订单`);

        // 检查该群组中是否有永久有效的订单
        const permanentBindingExists = await db.get(`
            SELECT id FROM bindings WHERE group_number = ? AND (expiration_time IS NULL OR remaining_days = -1) LIMIT 1
        `, [groupNumber]);

        // 检查该群组中是否已有激活的订单
        const activeBindingExists = await db.get(`
            SELECT id FROM bindings WHERE group_number = ? AND is_active = 1 LIMIT 1
        `, [groupNumber]);

        // 判断新订单的激活状态
        const isActive = !activeBindingExists ? 1 : 0; // 如果没有激活的订单，则该订单激活
        
        // 如果群组中已有永久有效的订单，新订单也设为永久
        let expirationTime = null;
        if (!permanentBindingExists) {
            // 仅用于显示预计到期时间，实际计费使用剩余天数
            const expirationDate = new Date();
            expirationDate.setDate(expirationDate.getDate() + remainingDays);
            expirationTime = expirationDate.toISOString();
        }
        
        const bindTime = new Date().toISOString();
        
        // 保存绑定信息
        await db.run(
            `INSERT INTO bindings (
                order_number, sku_id, group_number, owner, bind_time, 
                expiration_time, original_duration_months, remaining_days, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                orderNumber, 
                normalizedSkuId, 
                groupNumber, 
                req.user.username, 
                bindTime, 
                expirationTime, 
                months, 
                remainingDays, 
                isActive
            ]
        );
        
        // 管理群组内订单的激活状态
        await manageGroupActivation(groupNumber);
        
        const expirationDateDisplay = expirationTime 
            ? new Date(expirationTime).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }) 
            : '永久';
        
        logger.info(`订单 ${orderNumber} (SKU ${normalizedSkuId}) 已成功绑定到群组 ${groupNumber}, 剩余天数: ${remainingDays}, 激活状态: ${isActive ? '激活' : '待激活'}, SKU 类型: ${skuType}`);
        res.json({
            success: true,
            message: '绑定成功！',
            data: { 
                orderNumber, 
                groupNumber, 
                skuType, 
                remainingDays,
                isActive: isActive === 1,
                expirationDate: expirationDateDisplay 
            }
        });
    } catch (error) {
        logger.error('绑定过程中出错:', error);
        res.status(500).json({ success: false, message: `绑定失败: ${error.message}` });
    }
});

// 查询接口 - 针对单个订单的查询，现在主要作为备用功能
app.post('/query', authenticateToken, checkDatabaseConnection, [
    body('orderNumber').trim().notEmpty().withMessage('订单号不能为空。').isLength({ min: 5, max: 50 }).withMessage('订单号长度应在5到50个字符之间。')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        logger.warn('查询请求输入验证失败', { errors: errors.array(), body: req.body });
        return res.status(400).json({ success: false, message: errors.array().map(e => e.msg).join(' ') });
    }
    const { orderNumber } = req.body;
    try {
        logger.info(`收到查询请求: 用户: ${req.user.username}, 订单: ${orderNumber}`);
        
        // 查询绑定信息
        const binding = await db.get(
            'SELECT sku_id, group_number, expiration_time, remaining_days, is_active, owner FROM bindings WHERE order_number = ?',
            orderNumber
        );
        
        if (!binding) {
            logger.warn(`查询失败: 未找到订单 ${orderNumber} 的绑定信息。`);
            return res.status(404).json({ success: false, message: `未找到订单号 ${orderNumber} 的绑定信息。` });
        }
        
        // 检查当前用户是否是所有者
        if (binding.owner !== req.user.username) {
            logger.warn(`查询失败: 用户 ${req.user.username} 尝试查询不属于他的订单 ${orderNumber}。`);
            return res.status(403).json({ success: false, message: `您无权查询此订单信息，因为它不属于您。` });
        }
        const skuType = getSkuType(binding.sku_id);
        
        // 使用剩余天数或过期时间生成信息
        let statusMessage;
        
        if (binding.remaining_days === -1 || !binding.expiration_time) {
            statusMessage = '永久有效';
        } else if (binding.remaining_days <= 0) {
            statusMessage = '已过期';
        } else {
            statusMessage = `剩余 ${binding.remaining_days} 天`;
            if (binding.is_active === 1) {
                statusMessage += ' (当前激活中)';
            } else {
                statusMessage += ' (等待激活)';
            }
        }
        
        logger.info(`查询成功: 用户: ${req.user.username}, 订单 ${orderNumber}. 群组: ${binding.group_number}, SKU: ${skuType}, ${statusMessage}`);
        res.json({
            success: true,
            message: '查询成功！',
            data: {
                orderNumber,
                groupNumber: binding.group_number,
                skuType,
                remainingDays: binding.remaining_days,
                isActive: binding.is_active === 1,
                status: statusMessage
            }
        });
    } catch (error) {
        logger.error('查询过程中出错:', error);
        res.status(500).json({ success: false, message: `查询失败: ${error.message}` });
    }
});

// 用户资料更新
app.post('/user/profile', authenticateToken, checkDatabaseConnection, [
    body('email').optional().isEmail().withMessage('请提供有效的电子邮箱地址'),
    body('currentPassword').optional(),
    body('newPassword').optional().isLength({ min: 6 }).withMessage('新密码长度必须至少为6个字符')
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, message: errors.array().map(e => e.msg).join('; ') });
    }
    
    const { email, currentPassword, newPassword } = req.body;
    
    try {
        // 获取当前用户信息
        const user = await db.get('SELECT * FROM users WHERE username = ?', req.user.username);
        
        if (!user) {
            return res.status(404).json({ success: false, message: '用户不存在' });
        }
        
        let updated = false;
        const updates = [];
        const params = [];
        
        // 更新邮箱
        if (email && email !== user.email) {
            // 检查新邮箱是否已被使用
            const existingUser = await db.get('SELECT username FROM users WHERE email = ? AND username != ?', [email, req.user.username]);
            
            if (existingUser) {
                return res.status(409).json({ success: false, message: '该邮箱已被其他账户使用' });
            }
            
            updates.push('email = ?');
            params.push(email);
            updated = true;
        }
        
        // 更新密码
        if (newPassword && currentPassword) {
            // 验证当前密码
            const validPassword = await bcryptjs.compare(currentPassword, user.password);
            if (!validPassword) {
                return res.status(401).json({ success: false, message: '当前密码不正确' });
            }
            
            // 加密新密码
            const salt = await bcryptjs.genSalt(10);
            const hashedPassword = await bcryptjs.hash(newPassword, salt);
            
            updates.push('password = ?');
            params.push(hashedPassword);
            updated = true;
        }
        
        if (updated) {
            // 保存更新后的用户信息
            params.push(req.user.username);
            await db.run(
                `UPDATE users SET ${updates.join(', ')} WHERE username = ?`,
                params
            );
            
            logger.info(`用户 ${req.user.username} 更新了个人资料`);
            
            // 获取更新后的用户信息
            const updatedUser = await db.get(
                'SELECT username, email, register_time FROM users WHERE username = ?',
                req.user.username
            );
            
            res.json({
                success: true,
                message: '个人资料更新成功',
                data: updatedUser
            });
        } else {
            res.json({
                success: true,
                message: '没有信息需要更新',
                data: {
                    username: user.username,
                    email: user.email,
                    registerTime: user.register_time
                }
            });
        }
    } catch (error) {
        logger.error('更新用户资料时出错:', error);
        res.status(500).json({ success: false, message: `更新资料失败: ${error.message}` });
    }
});

// --- 修改：获取群组剩余天数 API ---
app.get('/user/group-expirations', authenticateToken, checkDatabaseConnection, async (req, res) => {
    try {
        logger.info(`收到获取用户群组总剩余天数请求: 用户: ${req.user.username}`);
        
        // 获取当前用户的所有绑定
        const userBindings = await db.all(`
            SELECT id, order_number, sku_id, group_number, remaining_days, is_active, expiration_time
            FROM bindings 
            WHERE owner = ? 
            ORDER BY group_number, bind_time ASC
        `, [req.user.username]);
        
        if (userBindings.length === 0) {
            return res.json({
                success: true,
                message: '用户没有绑定记录',
                data: []
            });
        }
        
        // 按群号分组
        const groupedBindings = {};
        userBindings.forEach(binding => {
            if (!groupedBindings[binding.group_number]) {
                groupedBindings[binding.group_number] = [];
            }
            groupedBindings[binding.group_number].push(binding);
        });

        const finalGroupedList = [];

        for (const groupNumber in groupedBindings) {
            const groupBindings = groupedBindings[groupNumber];
            
            // 使用辅助函数计算群组的剩余天数
            const { totalDays, hasPermanent, activeOrderId } = calculateGroupRemainingDays(groupBindings);
            
            // 查找当前激活的订单
            const activeBinding = groupBindings.find(b => b.is_active === 1);
            
            // 准备群组级别的信息
            const groupSkuTypes = new Set(groupBindings.map(b => getSkuType(b.sku_id)));

            finalGroupedList.push({
                groupNumber: groupNumber,
                skuTypes: Array.from(groupSkuTypes),
                totalRemainingDays: hasPermanent ? Infinity : totalDays,
                hasPermanent: hasPermanent,
                activeOrderNumber: activeBinding ? activeBinding.order_number : null,
                activeSkuType: activeBinding ? getSkuType(activeBinding.sku_id) : null,
                activeRemainingDays: activeBinding ? (activeBinding.remaining_days || 0) : 0
            });
        }
        
        logger.info(`成功获取用户 ${req.user.username} 的群组总剩余天数, 共 ${finalGroupedList.length} 个群组`);
        res.json({
            success: true,
            message: '获取群组总剩余天数成功',
            data: finalGroupedList
        });
    } catch (error) {
        logger.error('获取群组总剩余天数时出错:', error);
        res.status(500).json({ success: false, message: `获取群组总剩余天数失败: ${error.message}` });
    }
});

app.use((err, req, res, next) => {
    logger.error('未处理的服务器错误:', err);
    const isProduction = process.env.NODE_ENV === 'production';
    res.status(err.status || 500).json({
        success: false,
        message: isProduction ? '服务器发生内部错误，请稍后重试。' : `服务器错误: ${err.message}`,
    });
});

// 启动服务器
(async () => {
    try {
        const dbInitialized = await initializeDatabase();
        if (!dbInitialized) {
            console.error('无法初始化数据库，服务器退出。');
            process.exit(1);
        }
        
        // 迁移现有绑定数据
        await migrateExistingBindings();
        
        // 初始化定时任务
        setupDailyTask();
        
        app.listen(PORT, () => {
            logger.info(`服务器正在运行于 http://localhost:${PORT}`);
            logger.info(`用户前端页面位于 http://localhost:${PORT}/index.html`);
            logger.info(`后台管理页面位于 http://localhost:${PORT}/admin.html`);
        });
    } catch (error) {
        console.error('启动服务器时出错:', error);
        process.exit(1);
    }
})();